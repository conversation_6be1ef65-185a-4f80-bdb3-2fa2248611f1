/**
 * Inventory Management System
 * Handles inventory operations, stock movements, and reporting
 */
class InventoryManager {
    constructor() {
        this.apiService = new ApiService();
        this.inventory = [];
        this.movements = [];
        this.branches = [];
        this.filters = {
            search: '',
            status: '',
            branch: ''
        };
        
        this.init();
    }

    // Initialize the inventory manager
    init() {
        console.log('🏪 Inventory Manager Initialized');
        this.setupEventListeners();
        this.loadInitialData();
    }

    // Setup event listeners
    setupEventListeners() {
        // Search input
        const searchInput = document.getElementById('inventorySearch');
        if (searchInput) {
            console.log('✅ Inventory search input found');
            searchInput.addEventListener('input', this.debounce((e) => {
                console.log('🔍 Inventory search changed:', e.target.value);
                this.filters.search = e.target.value;
                this.loadInventory();
            }, 300));
        }

        // Status filter
        const statusFilter = document.getElementById('inventoryFilter');
        if (statusFilter) {
            console.log('✅ Inventory status filter found');
            statusFilter.addEventListener('change', (e) => {
                console.log('📊 Inventory status changed:', e.target.value);
                this.filters.status = e.target.value;
                this.loadInventory();
            });
        }

        console.log('🎯 Inventory event listeners setup complete');
    }

    // Load initial data
    async loadInitialData() {
        console.log('📊 Loading initial inventory data...');
        await Promise.all([
            this.loadInventory(),
            this.loadBranches(),
            this.loadInventorySummary()
        ]);
    }

    // Load inventory data
    async loadInventory() {
        try {
            this.showLoading(true);
            
            console.log('🔍 Loading inventory with filters:', this.filters);
            
            // Get all products with their inventory data
            const products = await this.apiService.get('/products');
            
            // Apply filters
            let filteredInventory = this.applyFilters(products);
            
            this.inventory = filteredInventory;
            console.log('📦 Loaded inventory items:', this.inventory.length);
            
            this.renderInventoryTable();
            this.updateInventoryStats();
            
        } catch (error) {
            console.error('Error loading inventory:', error);
            this.showNotification('خطأ في تحميل بيانات المخزون: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    // Apply filters to inventory
    applyFilters(products) {
        let filtered = [...products];
        
        // Apply search filter
        if (this.filters.search && this.filters.search.trim()) {
            const searchTerm = this.filters.search.toLowerCase().trim();
            console.log('🔍 Applying inventory search filter:', searchTerm);
            
            filtered = filtered.filter(product => {
                const name = (product.name || '').toLowerCase();
                const barcode = (product.barcode || '').toLowerCase();
                
                return name.includes(searchTerm) || barcode.includes(searchTerm);
            });
        }
        
        // Apply status filter
        if (this.filters.status && this.filters.status !== 'جميع الحالات') {
            console.log('📊 Applying inventory status filter:', this.filters.status);
            
            filtered = filtered.filter(product => {
                const quantity = product.currentQuantity || 0;
                const minStock = product.minStockLevel || 10;
                
                switch (this.filters.status) {
                    case 'متوفر':
                        return quantity > minStock;
                    case 'مخزون منخفض':
                        return quantity > 0 && quantity <= minStock;
                    case 'نفد المخزون':
                        return quantity === 0;
                    default:
                        return true;
                }
            });
        }
        
        return filtered;
    }

    // Load branches data
    async loadBranches() {
        try {
            // For now, use static branches data
            this.branches = [
                { id: 1, name: 'المحل الرئيسي', location: 'الرياض' },
                { id: 2, name: 'فرع جدة', location: 'جدة' },
                { id: 3, name: 'فرع الدمام', location: 'الدمام' }
            ];
            console.log('🏪 Loaded branches:', this.branches.length);
        } catch (error) {
            console.error('Error loading branches:', error);
        }
    }

    // Load inventory summary
    async loadInventorySummary() {
        try {
            const products = await this.apiService.get('/products');
            
            let totalValue = 0;
            let lowStockCount = 0;
            let outOfStockCount = 0;
            let totalItems = 0;
            
            products.forEach(product => {
                const quantity = product.currentQuantity || 0;
                const costPrice = product.defaultCostPrice || 0;
                const minStock = product.minStockLevel || 10;
                
                totalItems += quantity;
                totalValue += quantity * costPrice;
                
                if (quantity === 0) {
                    outOfStockCount++;
                } else if (quantity <= minStock) {
                    lowStockCount++;
                }
            });
            
            this.updateSummaryCards({
                totalValue,
                lowStockCount,
                outOfStockCount,
                totalItems
            });
            
        } catch (error) {
            console.error('Error loading inventory summary:', error);
        }
    }

    // Update summary cards
    updateSummaryCards(stats) {
        // Update total value
        const totalValueElement = document.querySelector('.inventory-total-value');
        if (totalValueElement) {
            totalValueElement.textContent = this.formatCurrency(stats.totalValue);
        }
        
        // Update low stock alerts
        const lowStockElement = document.querySelector('.inventory-low-stock');
        if (lowStockElement) {
            lowStockElement.textContent = stats.lowStockCount;
        }
        
        // Update out of stock
        const outOfStockElement = document.querySelector('.inventory-out-of-stock');
        if (outOfStockElement) {
            outOfStockElement.textContent = stats.outOfStockCount;
        }
        
        // Update total items
        const totalItemsElement = document.querySelector('.inventory-total-items');
        if (totalItemsElement) {
            totalItemsElement.textContent = stats.totalItems;
        }
    }

    // Render inventory table
    renderInventoryTable() {
        const tbody = document.getElementById('inventoryTableBody');
        if (!tbody) {
            console.log('❌ Inventory table body not found');
            return;
        }

        if (this.inventory.length === 0) {
            this.renderEmptyState();
            return;
        }

        tbody.innerHTML = this.inventory.map(product => `
            <tr class="hover:bg-gray-50" data-product-id="${product.id}">
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center space-x-3 space-x-reverse">
                        <div class="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center overflow-hidden">
                            ${product.imageUrl ? 
                                `<img src="${product.imageUrl}" alt="${product.name}" class="w-full h-full object-cover">` :
                                `<svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>`
                            }
                        </div>
                        <div>
                            <div class="text-sm font-medium text-gray-900">${product.name}</div>
                            <div class="text-sm text-gray-500">${product.barcode || 'لا يوجد باركود'}</div>
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${product.currentQuantity || 0}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">0</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">0</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${product.currentQuantity || 0}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                    ${this.renderStockBadge(product.currentQuantity || 0, product.minStockLevel || 10)}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${this.formatCurrency((product.currentQuantity || 0) * (product.defaultCostPrice || 0))}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${this.formatCurrency((product.currentQuantity || 0) * (product.defaultSellingPrice || 0))}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <button onclick="viewMovementHistory(${product.id})"
                                class="inline-flex items-center px-3 py-1.5 rounded-lg text-sm font-medium bg-purple-100 text-purple-700 hover:bg-purple-200 transition-colors">
                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            الحركات
                        </button>
                        <button onclick="adjustStock(${product.id})"
                                class="inline-flex items-center px-3 py-1.5 rounded-lg text-sm font-medium bg-green-100 text-green-700 hover:bg-green-200 transition-colors">
                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            تعديل
                        </button>
                        <button onclick="reorderProduct(${product.id})"
                                class="inline-flex items-center px-3 py-1.5 rounded-lg text-sm font-medium bg-blue-100 text-blue-700 hover:bg-blue-200 transition-colors">
                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            إعادة طلب
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    // Render stock badge
    renderStockBadge(quantity, minStock = 10) {
        let badgeClass = 'bg-green-100 text-green-800';
        let text = 'متوفر';

        if (quantity === 0) {
            badgeClass = 'bg-red-100 text-red-800';
            text = 'نفد المخزون';
        } else if (quantity <= minStock) {
            badgeClass = 'bg-yellow-100 text-yellow-800';
            text = 'مخزون منخفض';
        }

        return `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${badgeClass}">${text}</span>`;
    }

    // Render empty state
    renderEmptyState() {
        const tbody = document.getElementById('inventoryTableBody');
        if (!tbody) return;

        tbody.innerHTML = `
            <tr>
                <td colspan="9" class="px-6 py-4 text-center text-gray-500">
                    <div class="flex flex-col items-center justify-center py-8">
                        <svg class="w-12 h-12 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                        <p class="text-lg font-medium text-gray-900 mb-2">لا توجد عناصر في المخزون</p>
                        <p class="text-gray-500">لم يتم العثور على منتجات تطابق معايير البحث</p>
                    </div>
                </td>
            </tr>
        `;
    }

    // Show/hide loading state
    showLoading(show) {
        const tbody = document.getElementById('inventoryTableBody');
        
        if (show && tbody) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="px-6 py-4 text-center text-gray-500">
                        <div class="flex items-center justify-center">
                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            جاري تحميل بيانات المخزون...
                        </div>
                    </td>
                </tr>
            `;
        }
    }

    // Update inventory stats
    updateInventoryStats() {
        const statsElement = document.querySelector('.inventory-stats');
        if (statsElement) {
            statsElement.textContent = `${this.inventory.length} عنصر`;
        }
    }

    // Format currency
    formatCurrency(amount) {
        if (!amount) return '0.00 ر.س';
        return parseFloat(amount).toFixed(2) + ' ر.س';
    }

    // Debounce function
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Show notification
    showNotification(message, type = 'info') {
        if (typeof showNotification === 'function') {
            showNotification(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
}
