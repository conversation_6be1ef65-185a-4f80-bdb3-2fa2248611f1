{"name": "thingies", "version": "2.5.0", "description": "", "author": {"name": "<PERSON>ich", "url": "https://github.com/streamich"}, "homepage": "https://github.com/streamich/thingies", "repository": "streamich/thingies", "funding": {"type": "github", "url": "https://github.com/sponsors/streamich"}, "license": "MIT", "engines": {"node": ">=10.18"}, "main": "lib/index.js", "files": ["lib/", "LICENSE"], "types": "lib/index.d.ts", "typings": "lib/index.d.ts", "scripts": {"prettier": "prettier --ignore-path .gitignore --write 'src/**/*.{ts,tsx,js,jsx}'", "prettier:check": "prettier --check 'src/**/*.{ts,tsx,js,jsx}'", "tslint": "tslint 'src/**/*.{js,jsx,ts,tsx}' -t verbose", "lint": "yarn tslint", "clean": "rimraf lib es6 es2020 coverage typedocs gh-pages", "build": "tsc --project tsconfig.build.json --module commonjs --target es2020 --outDir lib", "test": "jest --no-cache --config='jest.config.js'", "coverage": "yarn test --collectCoverage", "typedoc": "npx typedoc@0.25.13 --tsconfig tsconfig.build.json", "build:pages": "npx rimraf@5.0.5 gh-pages && mkdir -p gh-pages && cp -r typedocs/* gh-pages && cp -r coverage gh-pages/coverage", "deploy:pages": "gh-pages -d gh-pages"}, "keywords": [], "dependencies": {}, "peerDependencies": {"tslib": "^2"}, "devDependencies": {"@types/benchmark": "^2.1.5", "@types/jest": "^29.5.12", "benchmark": "^2.1.4", "husky": "^8.0.0", "jest": "^29.7.0", "prettier": "^3.0.0", "pretty-quick": "^3.1.1", "rimraf": "^3.0.2", "ts-jest": "^29.1.2", "tslib": "^2.6.2", "tslint": "^6.1.3", "tslint-config-common": "^1.6.2", "typescript": "^5.0.3"}, "release": {"branches": ["main", {"name": "next", "prerelease": true}], "prepare": ["@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git"], "verifyConditions": ["@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git"]}}