# 🔧 حل مشاكل Backend - ElectroHub Pro

## ❌ **المشكلة الحالية:**
```
ERR_CONNECTION_REFUSED - لا يمكن الاتصال بـ https://localhost:7001
```

## ✅ **الحل السريع:**

### 1. **تشغيل Backend:**
```bash
# الطريقة الأولى - ملف التشغيل المبسط
run-backend-simple.bat

# الطريقة الثانية - يدوياً
cd Backend\ElectronicsStore.WebAPI
dotnet run
```

### 2. **التحقق من النجاح:**
بعد تشغيل Backend، يجب أن ترى:
```
Now listening on: https://localhost:7001
Now listening on: http://localhost:5001
Application started. Press Ctrl+C to shut down.
```

### 3. **اختبار الاتصال:**
افتح المتصفح واذهب إلى:
- https://localhost:7001/api/test
- http://localhost:5001/api/test

## 🔍 **تشخيص المشاكل:**

### **مشكلة 1: .NET غير مثبت**
```bash
dotnet --version
```
إذا لم يعمل، حمل .NET 8 SDK من:
https://dotnet.microsoft.com/download

### **مشكلة 2: المنفذ مستخدم**
```bash
netstat -ano | findstr :7001
netstat -ano | findstr :5001
```
إذا كان مستخدم، أغلق التطبيق أو غير المنفذ.

### **مشكلة 3: SSL Certificate**
```bash
dotnet dev-certs https --trust
```

### **مشكلة 4: قاعدة البيانات**
```bash
cd Backend\ElectronicsStore.WebAPI
dotnet ef database update
```

## 🚀 **الحلول البديلة:**

### **الحل 1: استخدام HTTP بدلاً من HTTPS**

1. **افتح:** `Backend\ElectronicsStore.WebAPI\Properties\launchSettings.json`

2. **ابحث عن:** `"ElectronicsStore.WebAPI"`

3. **غير `applicationUrl` إلى:**
```json
"applicationUrl": "http://localhost:5001"
```

4. **حدث `api.js`:**
```javascript
BASE_URLS: [
    'http://localhost:5001/api',
    'https://localhost:7001/api'
]
```

### **الحل 2: تشغيل على منفذ مختلف**

1. **في `launchSettings.json`:**
```json
"applicationUrl": "http://localhost:8080"
```

2. **في `api.js`:**
```javascript
BASE_URLS: [
    'http://localhost:8080/api'
]
```

## 📋 **خطوات التشخيص:**

### **الخطوة 1: فحص .NET**
```bash
dotnet --version
dotnet --list-sdks
```

### **الخطوة 2: فحص المشروع**
```bash
cd Backend\ElectronicsStore.WebAPI
dotnet restore
dotnet build
```

### **الخطوة 3: فحص قاعدة البيانات**
```bash
dotnet ef migrations list
dotnet ef database update
```

### **الخطوة 4: تشغيل مع تفاصيل**
```bash
dotnet run --verbosity detailed
```

## 🔧 **إعدادات متقدمة:**

### **تعطيل HTTPS مؤقت<|im_start|>:**
في `Program.cs` أو `Startup.cs`:
```csharp
// Comment out HTTPS redirection
// app.UseHttpsRedirection();
```

### **تعطيل SSL في Development:**
```bash
set ASPNETCORE_ENVIRONMENT=Development
dotnet run --no-https
```

### **استخدام Kestrel مباشرة:**
```bash
dotnet run --urls="http://localhost:5001"
```

## 📞 **الدعم السريع:**

### **إذا استمرت المشاكل:**

1. **أرسل لي نتيجة هذه الأوامر:**
```bash
dotnet --version
dotnet --info
cd Backend\ElectronicsStore.WebAPI
dotnet build
```

2. **أو جرب تشغيل Backend على منفذ HTTP:**
```bash
dotnet run --urls="http://localhost:5001"
```

3. **ثم حدث `api.js`:**
```javascript
BASE_URLS: ['http://localhost:5001/api']
```

## ✅ **علامات النجاح:**

### **في Terminal:**
```
info: Microsoft.Hosting.Lifetime[14]
      Now listening on: https://localhost:7001
info: Microsoft.Hosting.Lifetime[0]
      Application started. Press Ctrl+C to shut down.
```

### **في المتصفح:**
- https://localhost:7001/api/test يعطي JSON response
- صفحة login.html تظهر "النظام متصل"

### **في Console:**
```
✅ System Status: Online
📊 Backend: Backend يعمل بنجاح!
🔗 API URL: https://localhost:7001/api
```

---

## 🎯 **الخطوات التالية:**

1. **شغل Backend** باستخدام `run-backend-simple.bat`
2. **أعد تحميل** صفحة login.html
3. **تحقق من حالة النظام** في أسفل الصفحة
4. **جرب تسجيل الدخول** بالبيانات التجريبية

**إذا نجح Backend، ستختفي رسائل الخطأ وستظهر "النظام متصل"!** 🚀
