/* You can add global styles to this file, and also import other style files */

@import '@angular/material/prebuilt-themes/indigo-pink.css';

html, body { 
  height: 100%; 
  direction: rtl;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body { 
  margin: 0; 
  font-family: <PERSON><PERSON>, "Helvetica Neue", sans-serif; 
}

/* RTL Support */
.mat-drawer-container {
  direction: rtl;
}

.mat-drawer {
  direction: rtl;
}

/* Custom styles for Arabic text */
.mat-toolbar, .mat-card-title, .mat-card-subtitle {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Table styles */
.mat-mdc-table {
  direction: rtl;
}

.mat-mdc-header-cell, .mat-mdc-cell {
  text-align: right;
}

/* Form styles */
.mat-mdc-form-field {
  direction: rtl;
}

/* Button styles */
.mat-mdc-raised-button, .mat-mdc-flat-button {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Snackbar styles */
.mat-mdc-snack-bar-container {
  direction: rtl;
}
