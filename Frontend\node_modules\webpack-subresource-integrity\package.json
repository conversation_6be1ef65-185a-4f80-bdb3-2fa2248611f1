{"name": "webpack-subresource-integrity", "version": "5.1.0", "description": "Webpack plugin for enabling Subresource Integrity", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/waysact/webpack-subresource-integrity/tree/main/webpack-subresource-integrity#readme", "repository": {"type": "git", "url": "https://github.com/waysact/webpack-subresource-integrity.git"}, "bugs": {"url": "https://github.com/waysact/webpack-subresource-integrity/issues"}, "keywords": ["webpack", "plugin", "sri", "subresource", "integrity", "html-webpack-plugin"], "main": "./index.js", "types": "./webpack-subresource-integrity-public.d.ts", "engines": {"node": ">= 12"}, "dependencies": {"typed-assert": "^1.0.8"}, "peerDependencies": {"html-webpack-plugin": ">= 5.0.0-beta.1 < 6", "webpack": "^5.12.0"}, "peerDependenciesMeta": {"html-webpack-plugin": {"optional": true}}, "devDependencies": {"@microsoft/api-extractor": "^7.18.1", "@tsconfig/node12": "^1.0.9", "@types/cross-spawn": "^6.0.2", "@types/jest": "^26.0.24", "@types/json-schema": "^7.0.8", "@types/lodash": "^4.14.171", "@types/node": "^14.17.5", "@types/rimraf": "^3.0.1", "@types/tmp": "^0.2.1", "@typescript-eslint/eslint-plugin": "4.28.2", "@typescript-eslint/parser": "^4.28.2", "cross-spawn": "^7.0.3", "eslint": "^7.30.0", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "2.23.4", "eslint-plugin-jest": "^24.3.6", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-promise": "4.3.1", "eslint-plugin-standard": "4.1.0", "html-webpack-plugin": ">= 5.0.0-beta.1", "jest": "^26.6.3", "lodash": "^4.17.21", "memfs": "^3.2.0", "nyc": "*", "prettier": "^2.3.2", "rimraf": "^3.0.2", "tapable": "^2.2.0", "tmp": "^0.2.1", "tmp-promise": "^3.0.2", "ts-jest": "^26.5.6", "typescript": "^4.3.5", "webpack": "^5.44.0"}, "scripts": {"prepublish": "tsc && api-extractor run --local"}, "files": ["/index.js", "/index.js.map", "/plugin.js", "/plugin.js.map", "/reporter.js", "/reporter.js.map", "/util.js", "/util.js.map", "/types.js", "/types.js.map", "/webpack-subresource-integrity-public.d.ts", "/tsdoc-metadata.json", "/README.md", "/LICENSE"]}