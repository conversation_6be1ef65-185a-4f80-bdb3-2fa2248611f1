/**
 * Suppliers Management System
 * Handles supplier operations, CRUD, and supplier-related functionality
 */
class SuppliersManager {
    constructor() {
        this.apiService = new ApiService();
        this.suppliers = [];
        this.filters = {
            search: '',
            status: ''
        };
        
        this.init();
    }

    // Initialize suppliers manager
    init() {
        console.log('🏪 Suppliers Manager Initialized');
        this.setupEventListeners();
        this.loadSuppliers();
    }

    // Setup event listeners
    setupEventListeners() {
        // Search input
        const searchInput = document.getElementById('supplierSearch');
        if (searchInput) {
            console.log('✅ Supplier search input found');
            searchInput.addEventListener('input', this.debounce((e) => {
                console.log('🔍 Supplier search changed:', e.target.value);
                this.filters.search = e.target.value;
                this.filterSuppliers();
            }, 300));
        }

        // Status filter
        const statusFilter = document.getElementById('supplierStatusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filters.status = e.target.value;
                this.filterSuppliers();
            });
        }

        console.log('🎯 Suppliers event listeners setup complete');
    }

    // Load suppliers from API
    async loadSuppliers() {
        try {
            this.showLoading(true);
            console.log('📊 Loading suppliers...');
            
            this.suppliers = await this.apiService.get('/suppliers');
            console.log('🏪 Loaded suppliers:', this.suppliers.length);
            
            this.renderSuppliers();
            this.updateSuppliersStats();
            
        } catch (error) {
            console.error('Error loading suppliers:', error);
            this.showNotification('خطأ في تحميل الموردين: ' + error.message, 'error');
            this.renderEmptyState();
        } finally {
            this.showLoading(false);
        }
    }

    // Filter suppliers
    filterSuppliers() {
        let filtered = [...this.suppliers];
        
        // Apply search filter
        if (this.filters.search && this.filters.search.trim()) {
            const searchTerm = this.filters.search.toLowerCase().trim();
            filtered = filtered.filter(supplier => {
                const name = (supplier.name || '').toLowerCase();
                const email = (supplier.email || '').toLowerCase();
                const phone = (supplier.phone || '').toLowerCase();
                
                return name.includes(searchTerm) || 
                       email.includes(searchTerm) || 
                       phone.includes(searchTerm);
            });
        }
        
        // Apply status filter
        if (this.filters.status && this.filters.status !== 'all') {
            filtered = filtered.filter(supplier => {
                const isActive = supplier.isActive !== false;
                return this.filters.status === 'active' ? isActive : !isActive;
            });
        }
        
        this.renderSuppliers(filtered);
    }

    // Render suppliers table
    renderSuppliers(suppliersToRender = null) {
        const tbody = document.getElementById('suppliersTableBody');
        if (!tbody) {
            console.log('❌ Suppliers table body not found');
            return;
        }

        const suppliers = suppliersToRender || this.suppliers;

        if (suppliers.length === 0) {
            this.renderEmptyState();
            return;
        }

        tbody.innerHTML = suppliers.map(supplier => `
            <tr class="hover:bg-gray-50" data-supplier-id="${supplier.id}">
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center space-x-3 space-x-reverse">
                        <div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-gray-900">${supplier.name}</div>
                            <div class="text-sm text-gray-500">${supplier.contactPerson || 'غير محدد'}</div>
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${supplier.email || 'غير محدد'}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${supplier.phone || 'غير محدد'}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${supplier.address || 'غير محدد'}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                    ${this.renderStatusBadge(supplier.isActive !== false)}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${supplier.productsCount || 0} منتج</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <button onclick="editSupplier(${supplier.id})" class="text-primary-600 hover:text-primary-900">تعديل</button>
                        <button onclick="viewSupplierProducts(${supplier.id})" class="text-blue-600 hover:text-blue-900">المنتجات</button>
                        <button onclick="deleteSupplier(${supplier.id})" class="text-red-600 hover:text-red-900">حذف</button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    // Render status badge
    renderStatusBadge(isActive) {
        if (isActive) {
            return `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">نشط</span>`;
        } else {
            return `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">غير نشط</span>`;
        }
    }

    // Render empty state
    renderEmptyState() {
        const tbody = document.getElementById('suppliersTableBody');
        if (!tbody) return;

        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                    <div class="flex flex-col items-center justify-center py-8">
                        <svg class="w-12 h-12 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        <p class="text-lg font-medium text-gray-900 mb-2">لا يوجد موردين</p>
                        <p class="text-gray-500">لم يتم العثور على موردين يطابقون معايير البحث</p>
                    </div>
                </td>
            </tr>
        `;
    }

    // Show/hide loading state
    showLoading(show) {
        const tbody = document.getElementById('suppliersTableBody');
        
        if (show && tbody) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                        <div class="flex items-center justify-center">
                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            جاري تحميل الموردين...
                        </div>
                    </td>
                </tr>
            `;
        }
    }

    // Update suppliers stats
    updateSuppliersStats() {
        const totalSuppliers = this.suppliers.length;
        const activeSuppliers = this.suppliers.filter(s => s.isActive !== false).length;
        const inactiveSuppliers = totalSuppliers - activeSuppliers;

        // Update stats cards
        const totalElement = document.querySelector('.suppliers-total');
        const activeElement = document.querySelector('.suppliers-active');
        const inactiveElement = document.querySelector('.suppliers-inactive');

        if (totalElement) totalElement.textContent = totalSuppliers;
        if (activeElement) activeElement.textContent = activeSuppliers;
        if (inactiveElement) inactiveElement.textContent = inactiveSuppliers;
    }

    // Add new supplier
    async addSupplier(supplierData) {
        try {
            const newSupplier = await this.apiService.post('/suppliers', supplierData);
            this.suppliers.push(newSupplier);
            this.renderSuppliers();
            this.updateSuppliersStats();
            this.showNotification('تم إضافة المورد بنجاح', 'success');
            return newSupplier;
        } catch (error) {
            console.error('Error adding supplier:', error);
            this.showNotification('خطأ في إضافة المورد: ' + error.message, 'error');
            throw error;
        }
    }

    // Update supplier
    async updateSupplier(supplierId, supplierData) {
        try {
            const updatedSupplier = await this.apiService.put(`/suppliers/${supplierId}`, supplierData);
            const index = this.suppliers.findIndex(s => s.id === supplierId);
            if (index !== -1) {
                this.suppliers[index] = updatedSupplier;
                this.renderSuppliers();
                this.updateSuppliersStats();
            }
            this.showNotification('تم تحديث المورد بنجاح', 'success');
            return updatedSupplier;
        } catch (error) {
            console.error('Error updating supplier:', error);
            this.showNotification('خطأ في تحديث المورد: ' + error.message, 'error');
            throw error;
        }
    }

    // Delete supplier
    async deleteSupplier(supplierId) {
        if (!confirm('هل أنت متأكد من حذف هذا المورد؟')) {
            return;
        }

        try {
            await this.apiService.delete(`/suppliers/${supplierId}`);
            this.suppliers = this.suppliers.filter(s => s.id !== supplierId);
            this.renderSuppliers();
            this.updateSuppliersStats();
            this.showNotification('تم حذف المورد بنجاح', 'success');
        } catch (error) {
            console.error('Error deleting supplier:', error);
            this.showNotification('خطأ في حذف المورد: ' + error.message, 'error');
        }
    }

    // Get supplier by ID
    getSupplierById(supplierId) {
        return this.suppliers.find(s => s.id === supplierId);
    }

    // Open supplier modal
    openSupplierModal(supplier = null) {
        const modal = document.getElementById('supplierModal');
        if (!modal) {
            console.error('Supplier modal not found');
            return;
        }

        // Fill form if editing
        if (supplier) {
            document.getElementById('supplierId').value = supplier.id;
            document.getElementById('supplierName').value = supplier.name;
            document.getElementById('supplierEmail').value = supplier.email || '';
            document.getElementById('supplierPhone').value = supplier.phone || '';
            document.getElementById('supplierAddress').value = supplier.address || '';
            document.getElementById('supplierContactPerson').value = supplier.contactPerson || '';
            document.getElementById('supplierNotes').value = supplier.notes || '';
            
            document.getElementById('supplierModalTitle').textContent = 'تعديل المورد';
            document.getElementById('supplierSubmitBtn').textContent = 'تحديث المورد';
        } else {
            document.getElementById('supplierForm').reset();
            document.getElementById('supplierId').value = '';
            
            document.getElementById('supplierModalTitle').textContent = 'إضافة مورد جديد';
            document.getElementById('supplierSubmitBtn').textContent = 'إضافة المورد';
        }

        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    // Close supplier modal
    closeSupplierModal() {
        const modal = document.getElementById('supplierModal');
        if (modal) {
            modal.classList.remove('active');
            document.body.style.overflow = '';
        }
    }

    // Save supplier (add/edit)
    async saveSupplier(event) {
        event.preventDefault();

        const supplierId = document.getElementById('supplierId').value;
        const supplierData = {
            name: document.getElementById('supplierName').value,
            email: document.getElementById('supplierEmail').value || null,
            phone: document.getElementById('supplierPhone').value || null,
            address: document.getElementById('supplierAddress').value || null,
            contactPerson: document.getElementById('supplierContactPerson').value || null,
            notes: document.getElementById('supplierNotes').value || null,
            isActive: true
        };

        try {
            if (supplierId) {
                await this.updateSupplier(parseInt(supplierId), supplierData);
            } else {
                await this.addSupplier(supplierData);
            }

            this.closeSupplierModal();
        } catch (error) {
            // Error already handled in add/update methods
        }
    }

    // Debounce function
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Show notification
    showNotification(message, type = 'info') {
        if (typeof showNotification === 'function') {
            showNotification(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
}
