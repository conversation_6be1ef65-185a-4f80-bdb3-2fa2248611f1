@echo off
echo ========================================
echo    نظام إدارة المشتريات والمخزون
echo ========================================
echo.

echo تشغيل Backend (ASP.NET Core)...
start "Backend API" cmd /k "cd Backend && dotnet run --project ElectronicsStore.WebAPI"

echo انتظار تشغيل Backend...
timeout /t 5 /nobreak > nul

echo تشغيل Frontend (Angular)...
start "Frontend App" cmd /k "cd Frontend && npm start"

echo.
echo تم تشغيل النظام بنجاح!
echo Backend API: https://localhost:7000
echo Frontend App: http://localhost:4200
echo.
pause
