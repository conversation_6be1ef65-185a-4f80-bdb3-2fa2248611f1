@echo off
echo ========================================
echo    نظام إدارة المشتريات والمخزون
echo ========================================
echo.

echo فحص وتثبيت حزم Frontend...
cd Frontend
if not exist node_modules (
    echo تثبيت حزم npm...
    npm install
    if errorlevel 1 (
        echo خطأ في تثبيت حزم npm
        pause
        exit /b 1
    )
)
cd ..

echo تشغيل Backend (ASP.NET Core)...
start "Backend API" cmd /k "cd Backend && dotnet restore && dotnet run --project ElectronicsStore.WebAPI"

echo انتظار تشغيل Backend...
timeout /t 10 /nobreak > nul

echo تشغيل Frontend (Angular)...
start "Frontend App" cmd /k "cd Frontend && npm start"

echo.
echo تم تشغيل النظام بنجاح!
echo Backend API: https://localhost:7000
echo Frontend App: http://localhost:4200
echo.
echo ملاحظة: إذا واجهت مشاكل في Frontend، قم بتشغيل:
echo cd Frontend
echo npm install
echo npm start
echo.
pause
