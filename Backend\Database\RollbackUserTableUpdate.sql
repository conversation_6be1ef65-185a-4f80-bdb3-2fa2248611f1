-- ============================================================================
-- التراجع عن تحديث جدول المستخدمين
-- Rollback Users Table Update
-- ============================================================================
-- 
-- ⚠️ تحذير: هذا السكريبت سيحذف الحقول الجديدة والبيانات المرتبطة بها
-- ⚠️ WARNING: This script will remove new fields and associated data
--
-- استخدم هذا السكريبت فقط إذا كنت تريد التراجع عن التحديث
-- Use this script only if you want to rollback the update
--
-- ============================================================================

PRINT '⚠️ تحذير: بدء عملية التراجع عن تحديث جدول المستخدمين...';
PRINT '⚠️ WARNING: Starting rollback of users table update...';
PRINT '';

-- التأكد من أن المستخدم يريد المتابعة
PRINT '🛑 هل أنت متأكد من أنك تريد المتابعة؟';
PRINT '🛑 Are you sure you want to continue?';
PRINT '';
PRINT 'سيتم حذف الحقول التالية:';
PRINT 'The following fields will be deleted:';
PRINT '• email';
PRINT '• full_name';
PRINT '• phone_number';
PRINT '• is_active';
PRINT '• last_login_at';
PRINT '';
PRINT 'وسيتم تقليل حجم حقل password من 255 إلى 100';
PRINT 'And password field size will be reduced from 255 to 100';
PRINT '';

-- إزالة الفهارس أولاً
PRINT '🗑️ إزالة الفهارس...';
PRINT 'Removing indexes...';

-- إزالة فهرس البريد الإلكتروني
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('users') AND name = 'IX_users_email')
BEGIN
    DROP INDEX IX_users_email ON users;
    PRINT '✅ تم حذف فهرس البريد الإلكتروني';
END

-- إزالة فهرس اسم المستخدم (إذا كان مضاف بواسطة السكريبت)
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('users') AND name = 'IX_users_username')
BEGIN
    DROP INDEX IX_users_username ON users;
    PRINT '✅ تم حذف فهرس اسم المستخدم';
END

-- إزالة الحقول الجديدة
PRINT '';
PRINT '🗑️ إزالة الحقول الجديدة...';
PRINT 'Removing new fields...';

-- إزالة حقل البريد الإلكتروني
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('users') AND name = 'email')
BEGIN
    ALTER TABLE users DROP COLUMN email;
    PRINT '✅ تم حذف حقل email';
END

-- إزالة حقل الاسم الكامل
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('users') AND name = 'full_name')
BEGIN
    ALTER TABLE users DROP COLUMN full_name;
    PRINT '✅ تم حذف حقل full_name';
END

-- إزالة حقل رقم الهاتف
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('users') AND name = 'phone_number')
BEGIN
    ALTER TABLE users DROP COLUMN phone_number;
    PRINT '✅ تم حذف حقل phone_number';
END

-- إزالة حقل حالة المستخدم
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('users') AND name = 'is_active')
BEGIN
    ALTER TABLE users DROP COLUMN is_active;
    PRINT '✅ تم حذف حقل is_active';
END

-- إزالة حقل آخر تسجيل دخول
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('users') AND name = 'last_login_at')
BEGIN
    ALTER TABLE users DROP COLUMN last_login_at;
    PRINT '✅ تم حذف حقل last_login_at';
END

-- تقليل حجم حقل كلمة المرور
PRINT '';
PRINT '📏 تقليل حجم حقل كلمة المرور...';
PRINT 'Reducing password field size...';

-- التحقق من أن جميع كلمات المرور أقل من 100 حرف
DECLARE @longPasswordCount INT;
SELECT @longPasswordCount = COUNT(*) FROM users WHERE LEN(password) > 100;

IF @longPasswordCount > 0
BEGIN
    PRINT '⚠️ تحذير: يوجد ' + CAST(@longPasswordCount AS NVARCHAR(10)) + ' مستخدم بكلمة مرور أطول من 100 حرف';
    PRINT '⚠️ WARNING: There are ' + CAST(@longPasswordCount AS NVARCHAR(10)) + ' users with passwords longer than 100 characters';
    PRINT '⚠️ سيتم قطع كلمات المرور الطويلة!';
    PRINT '⚠️ Long passwords will be truncated!';
    
    -- قطع كلمات المرور الطويلة
    UPDATE users SET password = LEFT(password, 100) WHERE LEN(password) > 100;
    PRINT '⚠️ تم قطع كلمات المرور الطويلة';
END

-- تقليل حجم الحقل
ALTER TABLE users ALTER COLUMN password NVARCHAR(100) NOT NULL;
PRINT '✅ تم تقليل حجم حقل password إلى 100 حرف';

-- عرض النتيجة النهائية
PRINT '';
PRINT '📊 حالة الجدول بعد التراجع:';
PRINT '📊 Table status after rollback:';

SELECT 
    COLUMN_NAME as 'Column Name',
    DATA_TYPE as 'Data Type',
    CHARACTER_MAXIMUM_LENGTH as 'Max Length',
    IS_NULLABLE as 'Nullable'
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'users'
ORDER BY ORDINAL_POSITION;

PRINT '';
PRINT '============================================================================';
PRINT '✅ تم التراجع عن تحديث جدول المستخدمين بنجاح';
PRINT '✅ Users table update rollback completed successfully';
PRINT '';
PRINT '⚠️ ملاحظة: قد تحتاج إلى إعادة إنشاء الفهرس الفريد لاسم المستخدم';
PRINT '⚠️ NOTE: You may need to recreate the unique index for username';
PRINT '';
PRINT 'CREATE UNIQUE INDEX IX_users_username ON users(username);';
PRINT '============================================================================';
