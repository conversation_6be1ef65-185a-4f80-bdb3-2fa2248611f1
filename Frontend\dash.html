<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ElectroHub Pro 1.0 - نظام إدارة متجر الإلكترونيات</title>

    <!-- External CSS -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/dash-styles.css">

    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e'
                        },
                        gray: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 font-cairo">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200 fixed w-full top-0 z-50">
        <div class="flex items-center justify-between px-6 py-4">
            <div class="flex items-center space-x-4 space-x-reverse">
                <div class="flex items-center space-x-3 space-x-reverse">
                    <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-700 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">ElectroHub Pro</h1>
                        <p class="text-sm text-gray-500">الإصدار 1.0</p>
                    </div>
                </div>
            </div>
            
            <div class="flex items-center space-x-4 space-x-reverse">
                <div class="relative">
                    <input type="text" placeholder="البحث السريع..." class="w-80 px-4 py-2 pr-10 border border-gray-300 rounded-lg input-focus">
                    <svg class="w-5 h-5 text-gray-400 absolute right-3 top-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
                
                <div class="flex items-center space-x-3 space-x-reverse">
                    <button class="relative p-2 text-gray-600 hover:text-primary-600 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6l-6-6v6z"></path>
                        </svg>
                        <span class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full pulse-dot"></span>
                    </button>
                    
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%230ea5e9'/%3E%3Ctext x='20' y='26' text-anchor='middle' fill='white' font-size='16' font-weight='bold'%3Eأ%3C/text%3E%3C/svg%3E" alt="المستخدم" class="w-8 h-8 rounded-full">
                        <div class="text-right">
                            <p class="text-sm font-medium text-gray-900">أحمد محمد</p>
                            <p class="text-xs text-gray-500">مدير النظام</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <div class="flex pt-16">
        <!-- Sidebar -->
        <aside class="w-64 bg-white shadow-lg h-screen fixed right-0 overflow-y-auto">
            <nav class="p-4 space-y-2">
                <div class="mb-6">
                    <h2 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">لوحة التحكم الرئيسية</h2>
                </div>
                
                <a href="#dashboard" class="sidebar-item flex items-center space-x-3 space-x-reverse px-4 py-3 text-gray-700 rounded-lg bg-primary-50 text-primary-700" data-page="dashboard">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                    </svg>
                    <span>لوحة المعلومات</span>
                </a>
                
                <a href="#products" class="sidebar-item flex items-center space-x-3 space-x-reverse px-4 py-3 text-gray-700 rounded-lg hover:text-white" data-page="products">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                    <span>إدارة المنتجات</span>
                </a>
                
                <a href="#inventory" class="sidebar-item flex items-center space-x-3 space-x-reverse px-4 py-3 text-gray-700 rounded-lg hover:text-white" data-page="inventory">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                    <span>المخزون</span>
                </a>
                
                <a href="#pos" class="sidebar-item flex items-center space-x-3 space-x-reverse px-4 py-3 text-gray-700 rounded-lg hover:text-white" data-page="pos">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    <span>نقطة البيع</span>
                </a>
                
                <a href="#suppliers" class="sidebar-item flex items-center space-x-3 space-x-reverse px-4 py-3 text-gray-700 rounded-lg hover:text-white" data-page="suppliers">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    <span>الموردين</span>
                </a>
                
                <a href="#returns" class="sidebar-item flex items-center space-x-3 space-x-reverse px-4 py-3 text-gray-700 rounded-lg hover:text-white" data-page="returns">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                    </svg>
                    <span>المرتجعات</span>
                </a>
                
                <a href="#reports" class="sidebar-item flex items-center space-x-3 space-x-reverse px-4 py-3 text-gray-700 rounded-lg hover:text-white" data-page="reports">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <span>التقارير</span>
                </a>
                
                <div class="pt-4 mt-4 border-t border-gray-200">
                    <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">الإعدادات</h3>
                    <a href="#users" class="sidebar-item flex items-center space-x-3 space-x-reverse px-4 py-3 text-gray-700 rounded-lg hover:text-white" data-page="users">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                        <span>المستخدمين</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 mr-64 p-6">
            <!-- Dashboard Page -->
            <div id="dashboard-page" class="page-content fade-in">
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">مرحباً بك في ElectroHub Pro</h1>
                    <p class="text-gray-600">نظرة شاملة على أداء متجرك الإلكتروني</p>
                </div>

                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-gray-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">إجمالي المبيعات اليوم</p>
                                <p class="text-2xl font-bold text-gray-900">15,750 ر.س</p>
                                <p class="text-sm text-green-600 mt-1">+12.5% من أمس</p>
                            </div>
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-gray-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">المنتجات المتاحة</p>
                                <p class="text-2xl font-bold text-gray-900">2,847</p>
                                <p class="text-sm text-blue-600 mt-1">في 15 فئة</p>
                            </div>
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-gray-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">تنبيهات المخزون</p>
                                <p class="text-2xl font-bold text-gray-900">23</p>
                                <p class="text-sm text-orange-600 mt-1">منتج قارب على النفاد</p>
                            </div>
                            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover border border-gray-100">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">الطلبات المعلقة</p>
                                <p class="text-2xl font-bold text-gray-900">8</p>
                                <p class="text-sm text-purple-600 mt-1">تحتاج مراجعة</p>
                            </div>
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">إجراءات سريعة</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <button class="btn-primary text-white px-4 py-3 rounded-lg font-medium flex items-center justify-center space-x-2 space-x-reverse" onclick="openModal('addProductModal')">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                <span>إضافة منتج</span>
                            </button>
                            <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-3 rounded-lg font-medium flex items-center justify-center space-x-2 space-x-reverse transition-colors" onclick="showPage('pos')">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                                <span>فتح نقطة البيع</span>
                            </button>
                            <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-3 rounded-lg font-medium flex items-center justify-center space-x-2 space-x-reverse transition-colors" onclick="showPage('inventory')">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                                <span>جرد المخزون</span>
                            </button>
                            <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-3 rounded-lg font-medium flex items-center justify-center space-x-2 space-x-reverse transition-colors" onclick="showPage('reports')">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                <span>تقرير المبيعات</span>
                            </button>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">أحدث المعاملات</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center space-x-3 space-x-reverse">
                                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">فاتورة #1234</p>
                                        <p class="text-xs text-gray-500">منذ 5 دقائق</p>
                                    </div>
                                </div>
                                <span class="text-sm font-semibold text-green-600">+850 ر.س</span>
                            </div>
                            
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center space-x-3 space-x-reverse">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">إضافة منتج جديد</p>
                                        <p class="text-xs text-gray-500">منذ 15 دقيقة</p>
                                    </div>
                                </div>
                                <span class="text-sm text-gray-500">iPhone 15 Pro</span>
                            </div>
                            
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center space-x-3 space-x-reverse">
                                    <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">تنبيه مخزون منخفض</p>
                                        <p class="text-xs text-gray-500">منذ 30 دقيقة</p>
                                    </div>
                                </div>
                                <span class="text-sm text-orange-600">سماعات AirPods</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Section -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">مبيعات الأسبوع</h3>
                        <div class="h-64 flex items-end justify-between space-x-2 space-x-reverse">
                            <div class="flex flex-col items-center">
                                <div class="w-8 bg-primary-500 rounded-t" style="height: 120px;"></div>
                                <span class="text-xs text-gray-500 mt-2">السبت</span>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="w-8 bg-primary-400 rounded-t" style="height: 80px;"></div>
                                <span class="text-xs text-gray-500 mt-2">الأحد</span>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="w-8 bg-primary-500 rounded-t" style="height: 160px;"></div>
                                <span class="text-xs text-gray-500 mt-2">الاثنين</span>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="w-8 bg-primary-600 rounded-t" style="height: 200px;"></div>
                                <span class="text-xs text-gray-500 mt-2">الثلاثاء</span>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="w-8 bg-primary-500 rounded-t" style="height: 140px;"></div>
                                <span class="text-xs text-gray-500 mt-2">الأربعاء</span>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="w-8 bg-primary-700 rounded-t" style="height: 180px;"></div>
                                <span class="text-xs text-gray-500 mt-2">الخميس</span>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="w-8 bg-primary-600 rounded-t" style="height: 220px;"></div>
                                <span class="text-xs text-gray-500 mt-2">الجمعة</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">أفضل المنتجات مبيعاً</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3 space-x-reverse">
                                    <div class="w-10 h-10 bg-gray-200 rounded-lg"></div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">iPhone 15 Pro Max</p>
                                        <p class="text-xs text-gray-500">45 قطعة مباعة</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-semibold text-gray-900">67,500 ر.س</p>
                                    <div class="w-20 h-2 bg-gray-200 rounded-full mt-1">
                                        <div class="w-full h-2 bg-primary-500 rounded-full"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3 space-x-reverse">
                                    <div class="w-10 h-10 bg-gray-200 rounded-lg"></div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">Samsung Galaxy S24</p>
                                        <p class="text-xs text-gray-500">32 قطعة مباعة</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-semibold text-gray-900">38,400 ر.س</p>
                                    <div class="w-20 h-2 bg-gray-200 rounded-full mt-1">
                                        <div class="w-4/5 h-2 bg-primary-500 rounded-full"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3 space-x-reverse">
                                    <div class="w-10 h-10 bg-gray-200 rounded-lg"></div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">AirPods Pro</p>
                                        <p class="text-xs text-gray-500">28 قطعة مباعة</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-semibold text-gray-900">8,400 ر.س</p>
                                    <div class="w-20 h-2 bg-gray-200 rounded-full mt-1">
                                        <div class="w-3/5 h-2 bg-primary-500 rounded-full"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products Management Page -->
            <div id="products-page" class="page-content hidden fade-in">
                <div class="mb-8">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900 mb-2">إدارة المنتجات</h1>
                            <p class="text-gray-600">إضافة وتعديل وإدارة منتجات المتجر</p>
                        </div>
                        <button class="btn-primary text-white px-6 py-3 rounded-lg font-medium flex items-center space-x-2 space-x-reverse" onclick="openModal('addProductModal')">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            <span>إضافة منتج جديد</span>
                        </button>
                    </div>
                </div>

                <!-- Filters and Search -->
                <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-100 mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                            <input type="text" placeholder="اسم المنتج أو الباركود..." class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الفئة</label>
                            <select class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                                <option>جميع الفئات</option>
                                <option>الهواتف الذكية</option>
                                <option>أجهزة الكمبيوتر</option>
                                <option>الإكسسوارات</option>
                                <option>الأجهزة المنزلية</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">المورد</label>
                            <select class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                                <option>جميع الموردين</option>
                                <option>شركة التقنية المتقدمة</option>
                                <option>مؤسسة الإلكترونيات</option>
                                <option>متجر الأجهزة</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">حالة المخزون</label>
                            <select class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                                <option>جميع المنتجات</option>
                                <option>متوفر</option>
                                <option>مخزون منخفض</option>
                                <option>نفد المخزون</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Products Table -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المنتج</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الباركود</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الفئة</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">السعر</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المخزون</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المورد</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center space-x-3 space-x-reverse">
                                            <div class="w-12 h-12 bg-gray-200 rounded-lg"></div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">iPhone 15 Pro Max</div>
                                                <div class="text-sm text-gray-500">256GB - تيتانيوم طبيعي</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1234567890123</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">الهواتف الذكية</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">4,999 ر.س</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            45 قطعة
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">شركة التقنية المتقدمة</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <button class="text-primary-600 hover:text-primary-900">تعديل</button>
                                            <button class="text-red-600 hover:text-red-900">حذف</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center space-x-3 space-x-reverse">
                                            <div class="w-12 h-12 bg-gray-200 rounded-lg"></div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">Samsung Galaxy S24 Ultra</div>
                                                <div class="text-sm text-gray-500">512GB - أسود تيتانيوم</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1234567890124</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">الهواتف الذكية</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">4,299 ر.س</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            8 قطع
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">مؤسسة الإلكترونيات</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <button class="text-primary-600 hover:text-primary-900">تعديل</button>
                                            <button class="text-red-600 hover:text-red-900">حذف</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center space-x-3 space-x-reverse">
                                            <div class="w-12 h-12 bg-gray-200 rounded-lg"></div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">MacBook Pro 16"</div>
                                                <div class="text-sm text-gray-500">M3 Pro - 512GB</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1234567890125</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">أجهزة الكمبيوتر</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">9,999 ر.س</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            نفد المخزون
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">شركة التقنية المتقدمة</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <button class="text-primary-600 hover:text-primary-900">تعديل</button>
                                            <button class="text-red-600 hover:text-red-900">حذف</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200">
                        <div class="flex-1 flex justify-between sm:hidden">
                            <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">السابق</button>
                            <button class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">التالي</button>
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    عرض <span class="font-medium">1</span> إلى <span class="font-medium">10</span> من <span class="font-medium">97</span> نتيجة
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    <button class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">السابق</button>
                                    <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">1</button>
                                    <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-primary-50 text-sm font-medium text-primary-600">2</button>
                                    <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">3</button>
                                    <button class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">التالي</button>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- POS Page -->
            <div id="pos-page" class="page-content hidden fade-in">
                <div class="mb-6">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">نقطة البيع</h1>
                    <p class="text-gray-600">واجهة البيع السريع والفعال</p>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Product Search and List -->
                    <div class="lg:col-span-2">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                            <div class="mb-6">
                                <div class="relative">
                                    <input type="text" placeholder="البحث بالاسم أو مسح الباركود..." class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg input-focus text-lg">
                                    <svg class="w-6 h-6 text-gray-400 absolute right-3 top-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                            </div>

                            <!-- Product Categories -->
                            <div class="mb-6">
                                <div class="flex space-x-2 space-x-reverse overflow-x-auto pb-2">
                                    <button class="tab-active px-4 py-2 rounded-lg font-medium whitespace-nowrap">الكل</button>
                                    <button class="px-4 py-2 rounded-lg font-medium text-gray-600 hover:bg-gray-100 whitespace-nowrap">الهواتف</button>
                                    <button class="px-4 py-2 rounded-lg font-medium text-gray-600 hover:bg-gray-100 whitespace-nowrap">الكمبيوتر</button>
                                    <button class="px-4 py-2 rounded-lg font-medium text-gray-600 hover:bg-gray-100 whitespace-nowrap">الإكسسوارات</button>
                                    <button class="px-4 py-2 rounded-lg font-medium text-gray-600 hover:bg-gray-100 whitespace-nowrap">الأجهزة المنزلية</button>
                                </div>
                            </div>

                            <!-- Products Grid -->
                            <div class="grid grid-cols-2 md:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
                                <div class="border border-gray-200 rounded-lg p-4 hover:border-primary-300 cursor-pointer transition-colors" onclick="addToCart('iPhone 15 Pro', 4999)">
                                    <div class="w-full h-24 bg-gray-200 rounded-lg mb-3"></div>
                                    <h3 class="font-medium text-sm text-gray-900 mb-1">iPhone 15 Pro</h3>
                                    <p class="text-primary-600 font-bold">4,999 ر.س</p>
                                    <p class="text-xs text-gray-500">متوفر: 45</p>
                                </div>
                                
                                <div class="border border-gray-200 rounded-lg p-4 hover:border-primary-300 cursor-pointer transition-colors" onclick="addToCart('Samsung Galaxy S24', 3999)">
                                    <div class="w-full h-24 bg-gray-200 rounded-lg mb-3"></div>
                                    <h3 class="font-medium text-sm text-gray-900 mb-1">Samsung Galaxy S24</h3>
                                    <p class="text-primary-600 font-bold">3,999 ر.س</p>
                                    <p class="text-xs text-gray-500">متوفر: 23</p>
                                </div>
                                
                                <div class="border border-gray-200 rounded-lg p-4 hover:border-primary-300 cursor-pointer transition-colors" onclick="addToCart('AirPods Pro', 899)">
                                    <div class="w-full h-24 bg-gray-200 rounded-lg mb-3"></div>
                                    <h3 class="font-medium text-sm text-gray-900 mb-1">AirPods Pro</h3>
                                    <p class="text-primary-600 font-bold">899 ر.س</p>
                                    <p class="text-xs text-gray-500">متوفر: 67</p>
                                </div>
                                
                                <div class="border border-gray-200 rounded-lg p-4 hover:border-primary-300 cursor-pointer transition-colors" onclick="addToCart('MacBook Air', 4999)">
                                    <div class="w-full h-24 bg-gray-200 rounded-lg mb-3"></div>
                                    <h3 class="font-medium text-sm text-gray-900 mb-1">MacBook Air</h3>
                                    <p class="text-primary-600 font-bold">4,999 ر.س</p>
                                    <p class="text-xs text-gray-500">متوفر: 12</p>
                                </div>
                                
                                <div class="border border-gray-200 rounded-lg p-4 hover:border-primary-300 cursor-pointer transition-colors" onclick="addToCart('iPad Pro', 3499)">
                                    <div class="w-full h-24 bg-gray-200 rounded-lg mb-3"></div>
                                    <h3 class="font-medium text-sm text-gray-900 mb-1">iPad Pro</h3>
                                    <p class="text-primary-600 font-bold">3,499 ر.س</p>
                                    <p class="text-xs text-gray-500">متوفر: 18</p>
                                </div>
                                
                                <div class="border border-gray-200 rounded-lg p-4 hover:border-primary-300 cursor-pointer transition-colors" onclick="addToCart('Apple Watch', 1599)">
                                    <div class="w-full h-24 bg-gray-200 rounded-lg mb-3"></div>
                                    <h3 class="font-medium text-sm text-gray-900 mb-1">Apple Watch</h3>
                                    <p class="text-primary-600 font-bold">1,599 ر.س</p>
                                    <p class="text-xs text-gray-500">متوفر: 34</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Cart and Checkout -->
                    <div class="lg:col-span-1">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">سلة المشتريات</h3>
                            
                            <!-- Cart Items -->
                            <div id="cart-items" class="space-y-3 mb-6 max-h-64 overflow-y-auto">
                                <div class="text-center text-gray-500 py-8">
                                    <svg class="w-12 h-12 mx-auto mb-3 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m6 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"></path>
                                    </svg>
                                    <p>السلة فارغة</p>
                                    <p class="text-sm">اختر المنتجات لإضافتها</p>
                                </div>
                            </div>

                            <!-- Discount Section -->
                            <div class="border-t border-gray-200 pt-4 mb-4">
                                <div class="flex items-center space-x-2 space-x-reverse mb-3">
                                    <input type="text" placeholder="كود الخصم" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg input-focus">
                                    <button class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg font-medium transition-colors">تطبيق</button>
                                </div>
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    <input type="number" placeholder="خصم %" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg input-focus">
                                    <button class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg font-medium transition-colors">خصم</button>
                                </div>
                            </div>

                            <!-- Totals -->
                            <div class="border-t border-gray-200 pt-4 space-y-2">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">المجموع الفرعي:</span>
                                    <span id="subtotal">0.00 ر.س</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">الخصم:</span>
                                    <span id="discount" class="text-red-600">0.00 ر.س</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">الضريبة (15%):</span>
                                    <span id="tax">0.00 ر.س</span>
                                </div>
                                <div class="flex justify-between text-lg font-bold border-t border-gray-200 pt-2">
                                    <span>الإجمالي:</span>
                                    <span id="total" class="text-primary-600">0.00 ر.س</span>
                                </div>
                            </div>

                            <!-- Payment Methods -->
                            <div class="mt-6">
                                <h4 class="text-sm font-medium text-gray-900 mb-3">طريقة الدفع</h4>
                                <div class="grid grid-cols-2 gap-2">
                                    <button class="payment-method active px-3 py-2 border-2 border-primary-500 bg-primary-50 text-primary-700 rounded-lg font-medium text-sm">نقدي</button>
                                    <button class="payment-method px-3 py-2 border-2 border-gray-300 text-gray-700 rounded-lg font-medium text-sm hover:border-primary-300">بطاقة</button>
                                    <button class="payment-method px-3 py-2 border-2 border-gray-300 text-gray-700 rounded-lg font-medium text-sm hover:border-primary-300">تحويل</button>
                                    <button class="payment-method px-3 py-2 border-2 border-gray-300 text-gray-700 rounded-lg font-medium text-sm hover:border-primary-300">آجل</button>
                                </div>
                            </div>

                            <!-- Checkout Buttons -->
                            <div class="mt-6 space-y-3">
                                <button id="checkout-btn" class="w-full btn-primary text-white py-3 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed" disabled onclick="processCheckout()">
                                    إتمام البيع
                                </button>
                                <button class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 rounded-lg font-medium transition-colors" onclick="clearCart()">
                                    مسح السلة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Other pages would be implemented similarly -->
            <div id="inventory-page" class="page-content hidden fade-in">
                <div class="text-center py-20">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">صفحة المخزون</h2>
                    <p class="text-gray-600">سيتم تطوير هذه الصفحة قريباً</p>
                </div>
            </div>

            <div id="suppliers-page" class="page-content hidden fade-in">
                <div class="text-center py-20">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">صفحة الموردين</h2>
                    <p class="text-gray-600">سيتم تطوير هذه الصفحة قريباً</p>
                </div>
            </div>

            <div id="returns-page" class="page-content hidden fade-in">
                <div class="text-center py-20">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">صفحة المرتجعات</h2>
                    <p class="text-gray-600">سيتم تطوير هذه الصفحة قريباً</p>
                </div>
            </div>

            <div id="reports-page" class="page-content hidden fade-in">
                <div class="text-center py-20">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">صفحة التقارير</h2>
                    <p class="text-gray-600">سيتم تطوير هذه الصفحة قريباً</p>
                </div>
            </div>

            <div id="users-page" class="page-content hidden fade-in">
                <div class="text-center py-20">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">صفحة المستخدمين</h2>
                    <p class="text-gray-600">سيتم تطوير هذه الصفحة قريباً</p>
                </div>
            </div>
        </main>
    </div>

    <!-- Add Product Modal -->
    <div id="addProductModal" class="modal">
        <div class="modal-content w-full max-w-2xl">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-2xl font-bold text-gray-900">إضافة منتج جديد</h2>
                <button onclick="closeModal('addProductModal')" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <form class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">اسم المنتج *</label>
                        <input type="text" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الباركود/SKU *</label>
                        <input type="text" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الفئة *</label>
                        <div class="flex space-x-2 space-x-reverse">
                            <select required class="flex-1 px-4 py-2 border border-gray-300 rounded-lg input-focus">
                                <option value="">اختر الفئة</option>
                                <option>الهواتف الذكية</option>
                                <option>أجهزة الكمبيوتر</option>
                                <option>الإكسسوارات</option>
                                <option>الأجهزة المنزلية</option>
                            </select>
                            <button type="button" class="px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg">+</button>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">المورد *</label>
                        <select required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                            <option value="">اختر المورد</option>
                            <option>شركة التقنية المتقدمة</option>
                            <option>مؤسسة الإلكترونيات</option>
                            <option>متجر الأجهزة</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">السعر الافتراضي *</label>
                        <input type="number" step="0.01" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الحد الأدنى للسعر</label>
                        <input type="number" step="0.01" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">كمية المخزون *</label>
                        <input type="number" required class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus">
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                    <textarea rows="3" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus"></textarea>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">صورة المنتج</label>
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary-300 transition-colors">
                        <svg class="w-12 h-12 mx-auto mb-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        <p class="text-gray-600">اسحب الصورة هنا أو انقر للاختيار</p>
                        <input type="file" accept="image/*" class="hidden">
                    </div>
                </div>

                <div class="flex items-center justify-end space-x-4 space-x-reverse pt-6 border-t border-gray-200">
                    <button type="button" onclick="closeModal('addProductModal')" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium">
                        إلغاء
                    </button>
                    <button type="submit" class="btn-primary text-white px-6 py-2 rounded-lg font-medium">
                        حفظ المنتج
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Global variables
        let cart = [];
        let currentPage = 'dashboard';

        // Navigation functionality
        function showPage(pageId) {
            // Hide all pages
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.add('hidden');
            });
            
            // Show selected page
            document.getElementById(pageId + '-page').classList.remove('hidden');
            
            // Update sidebar active state
            document.querySelectorAll('aside a').forEach(link => {
                link.classList.remove('bg-primary-50', 'text-primary-700');
                link.classList.add('text-gray-700');
            });
            
            document.querySelector(`[data-page="${pageId}"]`).classList.add('bg-primary-50', 'text-primary-700');
            document.querySelector(`[data-page="${pageId}"]`).classList.remove('text-gray-700');
            
            currentPage = pageId;
        }

        // Sidebar navigation
        document.querySelectorAll('aside a').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const pageId = this.getAttribute('data-page');
                showPage(pageId);
            });
        });

        // Modal functionality
        function openModal(modalId) {
            document.getElementById(modalId).classList.add('active');
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('active');
        }

        // Close modal when clicking outside
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    this.classList.remove('active');
                }
            });
        });

        // POS Cart functionality
        function addToCart(productName, price) {
            const existingItem = cart.find(item => item.name === productName);
            
            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({
                    name: productName,
                    price: price,
                    quantity: 1
                });
            }
            
            updateCartDisplay();
            updateTotals();
        }

        function removeFromCart(productName) {
            cart = cart.filter(item => item.name !== productName);
            updateCartDisplay();
            updateTotals();
        }

        function updateQuantity(productName, newQuantity) {
            const item = cart.find(item => item.name === productName);
            if (item) {
                if (newQuantity <= 0) {
                    removeFromCart(productName);
                } else {
                    item.quantity = newQuantity;
                    updateCartDisplay();
                    updateTotals();
                }
            }
        }

        function updateCartDisplay() {
            const cartContainer = document.getElementById('cart-items');
            
            if (cart.length === 0) {
                cartContainer.innerHTML = `
                    <div class="text-center text-gray-500 py-8">
                        <svg class="w-12 h-12 mx-auto mb-3 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m6 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"></path>
                        </svg>
                        <p>السلة فارغة</p>
                        <p class="text-sm">اختر المنتجات لإضافتها</p>
                    </div>
                `;
            } else {
                cartContainer.innerHTML = cart.map(item => `
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex-1">
                            <h4 class="font-medium text-sm text-gray-900">${item.name}</h4>
                            <p class="text-xs text-gray-500">${item.price.toLocaleString()} ر.س × ${item.quantity}</p>
                        </div>
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <button onclick="updateQuantity('${item.name}', ${item.quantity - 1})" class="w-6 h-6 bg-gray-200 hover:bg-gray-300 rounded text-xs">-</button>
                            <span class="w-8 text-center text-sm">${item.quantity}</span>
                            <button onclick="updateQuantity('${item.name}', ${item.quantity + 1})" class="w-6 h-6 bg-gray-200 hover:bg-gray-300 rounded text-xs">+</button>
                            <button onclick="removeFromCart('${item.name}')" class="text-red-500 hover:text-red-700 ml-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                `).join('');
            }
        }

        function updateTotals() {
            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const discount = 0; // Can be calculated based on applied discounts
            const tax = subtotal * 0.15; // 15% VAT
            const total = subtotal - discount + tax;

            document.getElementById('subtotal').textContent = subtotal.toLocaleString() + ' ر.س';
            document.getElementById('discount').textContent = discount.toLocaleString() + ' ر.س';
            document.getElementById('tax').textContent = tax.toLocaleString() + ' ر.س';
            document.getElementById('total').textContent = total.toLocaleString() + ' ر.س';

            // Enable/disable checkout button
            const checkoutBtn = document.getElementById('checkout-btn');
            checkoutBtn.disabled = cart.length === 0;
        }

        function clearCart() {
            cart = [];
            updateCartDisplay();
            updateTotals();
        }

        function processCheckout() {
            if (cart.length === 0) return;
            
            // Simulate checkout process
            alert('تم إتمام البيع بنجاح!\nرقم الفاتورة: ' + Math.floor(Math.random() * 10000));
            clearCart();
        }

        // Payment method selection
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('payment-method')) {
                document.querySelectorAll('.payment-method').forEach(btn => {
                    btn.classList.remove('active', 'border-primary-500', 'bg-primary-50', 'text-primary-700');
                    btn.classList.add('border-gray-300', 'text-gray-700');
                });
                
                e.target.classList.add('active', 'border-primary-500', 'bg-primary-50', 'text-primary-700');
                e.target.classList.remove('border-gray-300', 'text-gray-700');
            }
        });

        // Search functionality
        document.querySelector('input[placeholder="البحث السريع..."]').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            console.log('Searching for:', searchTerm);
            // Implement search logic here
        });

        // Form submission
        document.querySelector('#addProductModal form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('تم حفظ المنتج بنجاح!');
            closeModal('addProductModal');
            this.reset();
        });

        // Initialize
        updateTotals();
        
        console.log('ElectroHub Pro 1.0 - نظام إدارة متجر الإلكترونيات تم تحميله بنجاح');
    </script>

    <!-- Custom JavaScript -->
    <script src="js/dash-script.js"></script>
<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'974e7985e679f9ee',t:'MTc1NjE2MDQwNS4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script></body>
</html>
