import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-inventory',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule],
  template: `
    <div class="inventory-container">
      <div class="header">
        <h1>إدارة المخزون</h1>
        <button mat-raised-button color="primary">
          <mat-icon>assessment</mat-icon>
          تقرير المخزون
        </button>
      </div>
      <mat-card>
        <mat-card-content>
          <p>سيتم إضافة إدارة المخزون والتقارير قريباً...</p>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .inventory-container { padding: 20px; }
    .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
  `]
})
export class InventoryComponent {}
