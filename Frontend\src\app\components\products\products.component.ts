import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snackbar';
import { ProductService } from '../../services/product.service';
import { Product } from '../../models/product.model';

@Component({
  selector: 'app-products',
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatSnackBarModule
  ],
  template: `
    <div class="products-container">
      <div class="header">
        <h1>إدارة المنتجات</h1>
        <button mat-raised-button color="primary" (click)="openAddDialog()">
          <mat-icon>add</mat-icon>
          إضافة منتج جديد
        </button>
      </div>

      <div class="table-container">
        <table mat-table [dataSource]="products" class="products-table">
          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef>الرقم</th>
            <td mat-cell *matCellDef="let product">{{ product.id }}</td>
          </ng-container>

          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef>اسم المنتج</th>
            <td mat-cell *matCellDef="let product">{{ product.name }}</td>
          </ng-container>

          <ng-container matColumnDef="barcode">
            <th mat-header-cell *matHeaderCellDef>الباركود</th>
            <td mat-cell *matCellDef="let product">{{ product.barcode || '-' }}</td>
          </ng-container>

          <ng-container matColumnDef="category">
            <th mat-header-cell *matHeaderCellDef>الصنف</th>
            <td mat-cell *matCellDef="let product">
              <mat-chip>{{ product.categoryName }}</mat-chip>
            </td>
          </ng-container>

          <ng-container matColumnDef="currentQuantity">
            <th mat-header-cell *matHeaderCellDef>الكمية الحالية</th>
            <td mat-cell *matCellDef="let product">
              <span [class]="getQuantityClass(product.currentQuantity)">
                {{ product.currentQuantity }}
              </span>
            </td>
          </ng-container>

          <ng-container matColumnDef="sellingPrice">
            <th mat-header-cell *matHeaderCellDef>سعر البيع</th>
            <td mat-cell *matCellDef="let product">
              {{ product.defaultSellingPrice | currency:'SAR':'symbol':'1.2-2' }}
            </td>
          </ng-container>

          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>الإجراءات</th>
            <td mat-cell *matCellDef="let product">
              <button mat-icon-button color="primary" (click)="editProduct(product)">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button color="accent" (click)="viewProduct(product)">
                <mat-icon>visibility</mat-icon>
              </button>
              <button mat-icon-button color="warn" (click)="deleteProduct(product)">
                <mat-icon>delete</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
      </div>
    </div>
  `,
  styles: [`
    .products-container {
      padding: 20px;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .table-container {
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .products-table {
      width: 100%;
    }

    .mat-mdc-header-cell {
      font-weight: bold;
      background-color: #f5f5f5;
    }

    .quantity-low {
      color: #f44336;
      font-weight: bold;
    }

    .quantity-medium {
      color: #ff9800;
      font-weight: bold;
    }

    .quantity-high {
      color: #4caf50;
      font-weight: bold;
    }
  `]
})
export class ProductsComponent implements OnInit {
  products: Product[] = [];
  displayedColumns: string[] = ['id', 'name', 'barcode', 'category', 'currentQuantity', 'sellingPrice', 'actions'];

  constructor(
    private productService: ProductService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit() {
    this.loadProducts();
  }

  loadProducts() {
    this.productService.getProducts().subscribe({
      next: (products) => {
        this.products = products;
      },
      error: (error) => {
        this.snackBar.open('خطأ في تحميل المنتجات', 'إغلاق', { duration: 3000 });
        console.error('Error loading products:', error);
      }
    });
  }

  getQuantityClass(quantity: number): string {
    if (quantity <= 5) return 'quantity-low';
    if (quantity <= 20) return 'quantity-medium';
    return 'quantity-high';
  }

  openAddDialog() {
    this.snackBar.open('سيتم إضافة نافذة إضافة المنتج قريباً', 'إغلاق', { duration: 3000 });
  }

  editProduct(product: Product) {
    this.snackBar.open('سيتم إضافة نافذة تعديل المنتج قريباً', 'إغلاق', { duration: 3000 });
  }

  viewProduct(product: Product) {
    this.snackBar.open('سيتم إضافة نافذة عرض تفاصيل المنتج قريباً', 'إغلاق', { duration: 3000 });
  }

  deleteProduct(product: Product) {
    if (confirm(`هل أنت متأكد من حذف المنتج "${product.name}"؟`)) {
      this.productService.deleteProduct(product.id).subscribe({
        next: () => {
          this.snackBar.open('تم حذف المنتج بنجاح', 'إغلاق', { duration: 3000 });
          this.loadProducts();
        },
        error: (error) => {
          this.snackBar.open('خطأ في حذف المنتج', 'إغلاق', { duration: 3000 });
          console.error('Error deleting product:', error);
        }
      });
    }
  }
}
