export interface Product {
  id: number;
  name: string;
  barcode?: string;
  categoryId: number;
  categoryName: string;
  supplierId?: number;
  supplierName?: string;
  defaultCostPrice: number;
  defaultSellingPrice: number;
  minSellingPrice: number;
  description?: string;
  createdAt: Date;
  currentQuantity: number;
}

export interface CreateProduct {
  name: string;
  barcode?: string;
  categoryId: number;
  supplierId?: number;
  defaultCostPrice: number;
  defaultSellingPrice: number;
  minSellingPrice: number;
  description?: string;
}

export interface UpdateProduct {
  id: number;
  name: string;
  barcode?: string;
  categoryId: number;
  supplierId?: number;
  defaultCostPrice: number;
  defaultSellingPrice: number;
  minSellingPrice: number;
  description?: string;
}
