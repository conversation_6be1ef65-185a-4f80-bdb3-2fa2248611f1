# 🔐 دليل تسجيل الدخول - ElectroHub Pro

## ✅ تم ربط نظام المصادقة بنجاح!

### 🎯 ما تم إنجازه:
- ✅ **صفحة تسجيل الدخول** مربوطة بـ ASP.NET API
- ✅ **JWT Authentication** - نظام مصادقة آمن
- ✅ **Auto-redirect** - توجيه تلقائي للوحة التحكم
- ✅ **Session Management** - إدارة جلسات المستخدمينw
- ✅ **Auto-logout** - تسجيل خروج تلقائي عند انتهاء الجلسة
- ✅ **User Info Display** - عرض معلومات المستخدم الحالي

## 🏃‍♂️ خطوات التشغيل:

### 1. تشغيل ASP.NET Backend
```bash
start-backend.bat
```
أو يدوياً:
```bash
cd Backend\ElectronicsStore.WebAPI
dotnet run
```

### 2. فتح صفحة تسجيل الدخول
افتح `Frontend/login.html` في المتصفح

### 3. تسجيل الدخول
استخدم البيانات التجريبية:
- **اسم المستخدم:** Ahmed
- **كلمة المرور:** Ahmed123!

## 🔗 مسار المصادقة:

### 1. تسجيل الدخول:
```
Frontend/login.html → ASP.NET API → JWT Token → Frontend/dash.html
```

### 2. حماية الصفحات:
- جميع الصفحات محمية بـ Authentication
- إعادة توجيه تلقائي لصفحة تسجيل الدخول إذا لم يكن المستخدم مسجل دخول

### 3. تسجيل الخروج:
- زر تسجيل الخروج في أعلى يمين الصفحة
- مسح البيانات المحفوظة
- إعادة توجيه لصفحة تسجيل الدخول

## 🔧 API Endpoints المستخدمة:

### Authentication:
- `POST /api/auth/login` - تسجيل الدخول
- `POST /api/auth/logout` - تسجيل الخروج
- `GET /api/auth/me` - معلومات المستخدم الحالي
- `POST /api/auth/validate-token` - التحقق من صحة Token

### System:
- `GET /api/test` - اختبار الاتصال
- `GET /api/test/database` - اختبار قاعدة البيانات

## 📊 بيانات المستخدم المحفوظة:

### في localStorage:
```javascript
{
  "authToken": "JWT_TOKEN_HERE",
  "refreshToken": "REFRESH_TOKEN_HERE", 
  "userData": {
    "id": 1,
    "username": "Ahmed",
    "fullName": "أحمد محمد",
    "email": "<EMAIL>",
    "roleName": "Super Admin",
    "permissions": ["..."]
  },
  "loginTime": "2024-01-01T00:00:00.000Z"
}
```

## 🛡️ الأمان:

### JWT Token:
- **انتهاء الصلاحية:** 60 دقيقة
- **تسجيل خروج تلقائي:** قبل دقيقة من انتهاء الصلاحية
- **Refresh Token:** لتجديد الجلسة

### حماية الصفحات:
- فحص المصادقة عند تحميل كل صفحة
- إعادة توجيه فورية للمستخدمين غير المصادقين

## 🎨 واجهة تسجيل الدخول:

### الميزات:
- **تصميم عربي** متجاوب
- **بيانات تجريبية** جاهزة للاختبار
- **رسائل خطأ** واضحة باللغة العربية
- **حالة الاتصال** مع Backend
- **تذكر المستخدم** (Remember Me)

### التفاعلات:
- **إظهار/إخفاء كلمة المرور**
- **تعبئة تلقائية** للبيانات التجريبية
- **رسائل نجاح وخطأ** متحركة
- **مؤشر تحميل** أثناء تسجيل الدخول

## 🔄 تدفق العمل:

### 1. المستخدم الجديد:
```
login.html → إدخال البيانات → API Call → JWT Token → dash.html
```

### 2. المستخدم المسجل مسبقاً:
```
login.html → فحص Token → dash.html (إعادة توجيه مباشرة)
```

### 3. انتهاء الجلسة:
```
dash.html → Token Expired → تنبيه → login.html
```

## 🐛 استكشاف الأخطاء:

### مشكلة في تسجيل الدخول:
1. تأكد من تشغيل Backend على المنفذ 7001
2. تحقق من البيانات: Ahmed / Ahmed123!
3. افتح Developer Tools للتحقق من الأخطاء

### مشكلة في الاتصال:
1. تحقق من حالة النظام في أسفل صفحة تسجيل الدخول
2. تأكد من إعدادات CORS في ASP.NET
3. تحقق من SSL Certificate

### مشكلة في إعادة التوجيه:
1. تأكد من وجود ملف dash.html
2. تحقق من JavaScript Console للأخطاء
3. امسح localStorage وحاول مرة أخرى

## 🎉 النتيجة النهائية:

الآن لديك نظام مصادقة متكامل مع:
- ✅ **تسجيل دخول آمن** مع JWT
- ✅ **حماية الصفحات** التلقائية
- ✅ **إدارة الجلسات** المتقدمة
- ✅ **واجهة عربية** جميلة
- ✅ **تجربة مستخدم** سلسة

**جاهز للاستخدام الفوري!** 🚀

---

## 📞 المساعدة:
1. تأكد من تشغيل Backend أولاً
2. استخدم البيانات التجريبية للاختبار
3. راجع Developer Tools للأخطاء
4. تحقق من حالة النظام في صفحة تسجيل الدخول
