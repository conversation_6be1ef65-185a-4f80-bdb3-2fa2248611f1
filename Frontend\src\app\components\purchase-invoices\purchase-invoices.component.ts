import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-purchase-invoices',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule],
  template: `
    <div class="purchase-invoices-container">
      <div class="header">
        <h1>فواتير الشراء</h1>
        <button mat-raised-button color="primary">
          <mat-icon>add</mat-icon>
          فاتورة شراء جديدة
        </button>
      </div>
      <mat-card>
        <mat-card-content>
          <p>سيتم إضافة إدارة فواتير الشراء قريباً...</p>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .purchase-invoices-container { padding: 20px; }
    .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
  `]
})
export class PurchaseInvoicesComponent {}
