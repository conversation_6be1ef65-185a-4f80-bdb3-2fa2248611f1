// ===== Authentication Management =====

// Setup login form
function setupLoginForm() {
    const loginForm = document.getElementById('loginForm');
    const loginBtn = document.getElementById('loginBtn');
    const loadingSpinner = document.getElementById('loadingSpinner');
    
    if (!loginForm) return;
    
    loginForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = new FormData(loginForm);
        const credentials = {
            username: formData.get('username').trim(),
            password: formData.get('password')
        };
        
        // Validate input
        if (!credentials.username || !credentials.password) {
            showError('يرجى إدخال اسم المستخدم وكلمة المرور');
            return;
        }
        
        // Show loading state
        setLoadingState(true);
        hideMessages();
        
        try {
            console.log('🔐 Attempting login for user:', credentials.username);
            
            // Call login API
            const response = await loginUser(credentials);
            
            console.log('✅ Login successful:', response.user);
            
            // Show success message
            showSuccess(`مرحباً ${response.user.fullName || response.user.username}!`);
            
            // Remember user if checkbox is checked
            const rememberMe = document.getElementById('rememberMe').checked;
            if (rememberMe) {
                localStorage.setItem('rememberUser', credentials.username);
            } else {
                localStorage.removeItem('rememberUser');
            }
            
            // Redirect to dashboard after short delay
            setTimeout(() => {
                window.location.href = 'dash.html';
            }, 1500);
            
        } catch (error) {
            console.error('❌ Login failed:', error);
            
            let errorMessage = formatApiError(error);
            
            // Handle specific error cases
            if (error.status === 401) {
                errorMessage = 'اسم المستخدم أو كلمة المرور غير صحيحة';
            } else if (error.status === 403) {
                errorMessage = 'حسابك غير مفعل. يرجى التواصل مع الإدارة';
            } else if (error.status === 429) {
                errorMessage = 'تم تجاوز عدد المحاولات المسموحة. يرجى المحاولة لاحقاً';
            } else if (!navigator.onLine) {
                errorMessage = 'لا يوجد اتصال بالإنترنت';
            }
            
            showError(errorMessage);
            
        } finally {
            setLoadingState(false);
        }
    });
    
    // Load remembered username
    loadRememberedUser();
}

// Toggle password visibility
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const passwordIcon = document.getElementById('passwordIcon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        passwordIcon.className = 'fas fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        passwordIcon.className = 'fas fa-eye';
    }
}

// Fill demo credentials
function fillDemoCredentials() {
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    
    if (usernameInput && passwordInput) {
        usernameInput.value = 'Ahmed';
        passwordInput.value = 'Ahmed123!';
        
        // Add visual feedback
        usernameInput.style.background = '#e6fffa';
        passwordInput.style.background = '#e6fffa';
        
        setTimeout(() => {
            usernameInput.style.background = '';
            passwordInput.style.background = '';
        }, 1000);
        
        showSuccess('تم تعبئة البيانات التجريبية');
    }
}

// Load remembered user
function loadRememberedUser() {
    const rememberedUser = localStorage.getItem('rememberUser');
    const usernameInput = document.getElementById('username');
    const rememberCheckbox = document.getElementById('rememberMe');
    
    if (rememberedUser && usernameInput && rememberCheckbox) {
        usernameInput.value = rememberedUser;
        rememberCheckbox.checked = true;
    }
}

// Set loading state
function setLoadingState(isLoading) {
    const loginForm = document.getElementById('loginForm');
    const loginBtn = document.getElementById('loginBtn');
    const loadingSpinner = document.getElementById('loadingSpinner');
    
    if (isLoading) {
        if (loginForm) loginForm.style.display = 'none';
        if (loadingSpinner) loadingSpinner.style.display = 'block';
        if (loginBtn) loginBtn.disabled = true;
    } else {
        if (loginForm) loginForm.style.display = 'block';
        if (loadingSpinner) loadingSpinner.style.display = 'none';
        if (loginBtn) loginBtn.disabled = false;
    }
}

// Show error message
function showError(message) {
    const errorElement = document.getElementById('errorMessage');
    const errorText = document.getElementById('errorText');
    
    if (errorElement && errorText) {
        errorText.textContent = message;
        errorElement.style.display = 'flex';
        
        // Auto hide after 5 seconds
        setTimeout(() => {
            errorElement.style.display = 'none';
        }, 5000);
    }
    
    console.error('🚨 Error:', message);
}

// Show success message
function showSuccess(message) {
    const successElement = document.getElementById('successMessage');
    const successText = document.getElementById('successText');
    
    if (successElement && successText) {
        successText.textContent = message;
        successElement.style.display = 'flex';
        
        // Auto hide after 3 seconds
        setTimeout(() => {
            successElement.style.display = 'none';
        }, 3000);
    }
    
    console.log('✅ Success:', message);
}

// Hide all messages
function hideMessages() {
    const errorElement = document.getElementById('errorMessage');
    const successElement = document.getElementById('successMessage');
    
    if (errorElement) errorElement.style.display = 'none';
    if (successElement) successElement.style.display = 'none';
}

// Check authentication status
function checkAuthStatus() {
    if (isAuthenticated()) {
        const userData = getStoredUserData();
        console.log('👤 User is authenticated:', userData);
        return true;
    } else {
        console.log('🚫 User is not authenticated');
        return false;
    }
}

// Redirect to login if not authenticated
function requireAuth() {
    if (!checkAuthStatus()) {
        console.log('🔒 Redirecting to login page');
        window.location.href = 'login.html';
        return false;
    }
    return true;
}

// Logout function
async function logout() {
    try {
        console.log('🚪 Logging out user');
        
        // Call logout API
        await logoutUser();
        
        // Redirect to login page
        window.location.href = 'login.html';
        
    } catch (error) {
        console.error('❌ Logout error:', error);
        
        // Force redirect even if API fails
        window.location.href = 'login.html';
    }
}

// Auto-logout on token expiration
function setupAutoLogout() {
    const token = getAuthToken();
    if (!token) return;
    
    try {
        // Decode JWT token to get expiration time
        const payload = JSON.parse(atob(token.split('.')[1]));
        const expirationTime = payload.exp * 1000; // Convert to milliseconds
        const currentTime = Date.now();
        const timeUntilExpiration = expirationTime - currentTime;
        
        if (timeUntilExpiration > 0) {
            // Set timeout to logout before token expires
            setTimeout(() => {
                console.log('⏰ Token expired, logging out');
                showError('انتهت جلسة العمل. يرجى تسجيل الدخول مرة أخرى');
                setTimeout(() => logout(), 2000);
            }, timeUntilExpiration - 60000); // Logout 1 minute before expiration
            
            console.log(`⏰ Auto-logout scheduled in ${Math.round(timeUntilExpiration / 1000 / 60)} minutes`);
        } else {
            console.log('⏰ Token already expired');
            logout();
        }
        
    } catch (error) {
        console.error('❌ Error parsing token:', error);
    }
}

// Initialize authentication
function initAuth() {
    // Check if we're on login page
    const isLoginPage = window.location.pathname.includes('login.html') || 
                       window.location.pathname === '/' || 
                       window.location.pathname.endsWith('/');
    
    if (isLoginPage) {
        // If already authenticated, redirect to dashboard
        if (checkAuthStatus()) {
            console.log('👤 User already authenticated, redirecting to dashboard');
            window.location.href = 'dash.html';
            return;
        }
    } else {
        // For other pages, require authentication
        if (!requireAuth()) {
            return;
        }
        
        // Setup auto-logout
        setupAutoLogout();
    }
}

// Run authentication check when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initAuth();
});

// Handle online/offline status
window.addEventListener('online', function() {
    console.log('🌐 Connection restored');
    checkSystemStatus();
});

window.addEventListener('offline', function() {
    console.log('📡 Connection lost');
    showError('فقد الاتصال بالإنترنت');
});
