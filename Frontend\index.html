<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المتجر الإلكتروني</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .main-layout {
            display: flex;
            min-height: 100vh;
            background: #f8fafc;
        }
        
        .sidebar {
            width: 280px;
            background: white;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
        }
        
        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            text-align: center;
        }
        
        .sidebar-header .logo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            margin-bottom: 0.5rem;
        }
        
        .sidebar-header .logo i {
            font-size: 2rem;
            color: #3b82f6;
        }
        
        .sidebar-header h1 {
            font-size: 1.25rem;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }
        
        .sidebar-header p {
            font-size: 0.875rem;
            color: #64748b;
            margin: 0;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .nav-item {
            display: block;
            padding: 1rem 1.5rem;
            color: #64748b;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            background: none;
            width: 100%;
            text-align: right;
            cursor: pointer;
        }
        
        .nav-item:hover {
            background: #f1f5f9;
            color: #3b82f6;
        }
        
        .nav-item.active {
            background: #eff6ff;
            color: #3b82f6;
            border-left: 4px solid #3b82f6;
        }
        
        .nav-item i {
            margin-left: 0.75rem;
            width: 20px;
            text-align: center;
        }
        
        .content-area {
            flex: 1;
            margin-right: 280px;
            min-height: 100vh;
        }
        
        .page-content {
            display: none;
        }
        
        .page-content.active {
            display: block;
        }
        
        .page-header {
            background: white;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .page-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .page-body {
            padding: 2rem;
        }
        
        .coming-soon {
            text-align: center;
            padding: 4rem 2rem;
            color: #64748b;
        }
        
        .coming-soon i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        .coming-soon h2 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: #1e293b;
        }
        
        .coming-soon p {
            font-size: 1rem;
        }
    </style>
</head>
<body>
    <div class="main-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-store"></i>
                    <h1>ElectroHub Pro</h1>
                </div>
                <p>نظام إدارة المتجر</p>
            </div>
            
            <nav class="sidebar-nav">
                <button class="nav-item active" onclick="showPage('dashboard')">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم الرئيسية
                </button>
                
                <button class="nav-item" onclick="showPage('products')">
                    <i class="fas fa-box"></i>
                    لوحة المعلومات
                </button>
                
                <button class="nav-item" onclick="showPage('categories')">
                    <i class="fas fa-tags"></i>
                    إدارة المنتجات
                </button>
                
                <button class="nav-item" onclick="showPage('inventory')">
                    <i class="fas fa-warehouse"></i>
                    المخزون
                </button>
                
                <button class="nav-item" onclick="showPage('sales')">
                    <i class="fas fa-shopping-cart"></i>
                    نقطة البيع
                </button>
                
                <button class="nav-item" onclick="showPage('suppliers')">
                    <i class="fas fa-truck"></i>
                    الموردين
                </button>
                
                <button class="nav-item" onclick="showPage('reports')">
                    <i class="fas fa-chart-bar"></i>
                    المراجعات
                </button>
                
                <button class="nav-item" onclick="showPage('analytics')">
                    <i class="fas fa-chart-line"></i>
                    التقارير
                </button>
                
                <button class="nav-item" onclick="showPage('settings')">
                    <i class="fas fa-cog"></i>
                    الإعدادات
                </button>
                
                <button class="nav-item" onclick="showPage('users')">
                    <i class="fas fa-users"></i>
                    المستخدمين
                </button>
            </nav>
        </aside>
        
        <!-- Content Area -->
        <main class="content-area">
            <!-- Dashboard Page -->
            <div id="dashboard" class="page-content active">
                <div class="page-header">
                    <h1 class="page-title">
                        <i class="fas fa-tachometer-alt"></i>
                        لوحة التحكم الرئيسية
                    </h1>
                </div>
                <div class="page-body">
                    <!-- Dashboard content will be loaded here -->
                    <iframe src="dashboard.html" style="width: 100%; height: 800px; border: none; border-radius: 8px;"></iframe>
                </div>
            </div>
            
            <!-- Other Pages -->
            <div id="products" class="page-content">
                <div class="page-header">
                    <h1 class="page-title">
                        <i class="fas fa-box"></i>
                        لوحة المعلومات
                    </h1>
                </div>
                <div class="page-body">
                    <div class="coming-soon">
                        <i class="fas fa-info-circle"></i>
                        <h2>لوحة المعلومات</h2>
                        <p>هذه الصفحة قيد التطوير</p>
                    </div>
                </div>
            </div>
            
            <div id="categories" class="page-content">
                <div class="page-header">
                    <h1 class="page-title">
                        <i class="fas fa-tags"></i>
                        إدارة المنتجات
                    </h1>
                </div>
                <div class="page-body">
                    <div class="coming-soon">
                        <i class="fas fa-box"></i>
                        <h2>إدارة المنتجات</h2>
                        <p>هذه الصفحة قيد التطوير</p>
                    </div>
                </div>
            </div>
            
            <div id="inventory" class="page-content">
                <div class="page-header">
                    <h1 class="page-title">
                        <i class="fas fa-warehouse"></i>
                        المخزون
                    </h1>
                </div>
                <div class="page-body">
                    <div class="coming-soon">
                        <i class="fas fa-warehouse"></i>
                        <h2>إدارة المخزون</h2>
                        <p>هذه الصفحة قيد التطوير</p>
                    </div>
                </div>
            </div>
            
            <div id="sales" class="page-content">
                <div class="page-header">
                    <h1 class="page-title">
                        <i class="fas fa-shopping-cart"></i>
                        نقطة البيع
                    </h1>
                </div>
                <div class="page-body">
                    <div class="coming-soon">
                        <i class="fas fa-shopping-cart"></i>
                        <h2>نقطة البيع</h2>
                        <p>هذه الصفحة قيد التطوير</p>
                    </div>
                </div>
            </div>
            
            <div id="suppliers" class="page-content">
                <div class="page-header">
                    <h1 class="page-title">
                        <i class="fas fa-truck"></i>
                        الموردين
                    </h1>
                </div>
                <div class="page-body">
                    <div class="coming-soon">
                        <i class="fas fa-truck"></i>
                        <h2>إدارة الموردين</h2>
                        <p>هذه الصفحة قيد التطوير</p>
                    </div>
                </div>
            </div>
            
            <div id="reports" class="page-content">
                <div class="page-header">
                    <h1 class="page-title">
                        <i class="fas fa-chart-bar"></i>
                        المراجعات
                    </h1>
                </div>
                <div class="page-body">
                    <div class="coming-soon">
                        <i class="fas fa-chart-bar"></i>
                        <h2>المراجعات</h2>
                        <p>هذه الصفحة قيد التطوير</p>
                    </div>
                </div>
            </div>
            
            <div id="analytics" class="page-content">
                <div class="page-header">
                    <h1 class="page-title">
                        <i class="fas fa-chart-line"></i>
                        التقارير
                    </h1>
                </div>
                <div class="page-body">
                    <div class="coming-soon">
                        <i class="fas fa-chart-line"></i>
                        <h2>التقارير والتحليلات</h2>
                        <p>هذه الصفحة قيد التطوير</p>
                    </div>
                </div>
            </div>
            
            <div id="settings" class="page-content">
                <div class="page-header">
                    <h1 class="page-title">
                        <i class="fas fa-cog"></i>
                        الإعدادات
                    </h1>
                </div>
                <div class="page-body">
                    <div class="coming-soon">
                        <i class="fas fa-cog"></i>
                        <h2>إعدادات النظام</h2>
                        <p>هذه الصفحة قيد التطوير</p>
                    </div>
                </div>
            </div>
            
            <div id="users" class="page-content">
                <div class="page-header">
                    <h1 class="page-title">
                        <i class="fas fa-users"></i>
                        المستخدمين
                    </h1>
                </div>
                <div class="page-body">
                    <div class="coming-soon">
                        <i class="fas fa-users"></i>
                        <h2>إدارة المستخدمين</h2>
                        <p>هذه الصفحة قيد التطوير</p>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        function showPage(pageId) {
            // Hide all pages
            const pages = document.querySelectorAll('.page-content');
            pages.forEach(page => page.classList.remove('active'));
            
            // Show selected page
            const selectedPage = document.getElementById(pageId);
            if (selectedPage) {
                selectedPage.classList.add('active');
            }
            
            // Update navigation
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => item.classList.remove('active'));
            
            // Find and activate the clicked nav item
            const clickedItem = event.target.closest('.nav-item');
            if (clickedItem) {
                clickedItem.classList.add('active');
            }
        }
    </script>
</body>
</html>
