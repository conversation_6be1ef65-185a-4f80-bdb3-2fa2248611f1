-- ============================================================================
-- تحديث جدول المستخدمين لدعم نظام المصادقة الجديد
-- Update Users Table for New Authentication System
-- ============================================================================
--
-- هذا السكريبت آمن للتشغيل على قاعدة بيانات تحتوي على بيانات موجودة
-- This script is safe to run on a database with existing data
--
-- الخطوات:
-- 1. إضافة الحقول الجديدة
-- 2. تحديث البيانات الموجودة بقيم افتراضية
-- 3. إنشاء الفهارس المطلوبة
-- 4. التحقق من النتائج
-- ============================================================================

PRINT '🚀 بدء تحديث جدول المستخدمين...';
PRINT 'Starting users table update...';
PRINT '';

-- التحقق من وجود الحقول الجديدة قبل إضافتها
-- Check if new columns exist before adding them

-- 1. إضافة حقل البريد الإلكتروني
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('users') AND name = 'email')
BEGIN
    ALTER TABLE users ADD email NVARCHAR(100) NULL;
    PRINT 'تم إضافة حقل email';
END
ELSE
BEGIN
    PRINT 'حقل email موجود بالفعل';
END

-- 2. إضافة حقل الاسم الكامل
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('users') AND name = 'full_name')
BEGIN
    ALTER TABLE users ADD full_name NVARCHAR(100) NULL;
    PRINT 'تم إضافة حقل full_name';
END
ELSE
BEGIN
    PRINT 'حقل full_name موجود بالفعل';
END

-- 3. إضافة حقل رقم الهاتف
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('users') AND name = 'phone_number')
BEGIN
    ALTER TABLE users ADD phone_number NVARCHAR(20) NULL;
    PRINT 'تم إضافة حقل phone_number';
END
ELSE
BEGIN
    PRINT 'حقل phone_number موجود بالفعل';
END

-- 4. إضافة حقل حالة المستخدم
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('users') AND name = 'is_active')
BEGIN
    ALTER TABLE users ADD is_active BIT NOT NULL DEFAULT 1;
    PRINT 'تم إضافة حقل is_active';
END
ELSE
BEGIN
    PRINT 'حقل is_active موجود بالفعل';
END

-- 5. إضافة حقل آخر تسجيل دخول
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('users') AND name = 'last_login_at')
BEGIN
    ALTER TABLE users ADD last_login_at DATETIME NULL;
    PRINT 'تم إضافة حقل last_login_at';
END
ELSE
BEGIN
    PRINT 'حقل last_login_at موجود بالفعل';
END

-- 6. تحديث حجم حقل كلمة المرور
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('users') AND name = 'password' AND max_length < 510)
BEGIN
    ALTER TABLE users ALTER COLUMN password NVARCHAR(255) NOT NULL;
    PRINT 'تم تحديث حجم حقل password إلى 255 حرف';
END
ELSE
BEGIN
    PRINT 'حقل password بالحجم الصحيح بالفعل';
END

-- 7. تحديث البيانات الموجودة بقيم افتراضية آمنة
PRINT '';
PRINT '📝 تحديث البيانات الموجودة...';
PRINT 'Updating existing data...';

-- تحديث البريد الإلكتروني للمستخدمين الذين لا يملكون بريد
UPDATE users
SET email = CONCAT(username, '@temp.local')
WHERE email IS NULL OR email = '';

PRINT '✅ تم تحديث البريد الإلكتروني للمستخدمين الموجودين';

-- تحديث الأسماء الكاملة للمستخدمين الموجودين
UPDATE users
SET full_name = username
WHERE full_name IS NULL OR full_name = '';

PRINT '✅ تم تحديث الأسماء الكاملة للمستخدمين الموجودين';

-- تفعيل جميع المستخدمين الموجودين
UPDATE users
SET is_active = 1
WHERE is_active IS NULL;

PRINT '✅ تم تفعيل جميع المستخدمين الموجودين';

-- 8. إضافة فهرس فريد للبريد الإلكتروني
PRINT '';
PRINT '🔍 إنشاء الفهارس...';
PRINT 'Creating indexes...';

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('users') AND name = 'IX_users_email')
BEGIN
    -- إنشاء الفهرس الفريد
    CREATE UNIQUE INDEX IX_users_email ON users(email);
    PRINT '✅ تم إنشاء فهرس فريد للبريد الإلكتروني';
END
ELSE
BEGIN
    PRINT '⚠️ فهرس البريد الإلكتروني موجود بالفعل';
END

-- 9. إضافة فهرس فريد لاسم المستخدم (إذا لم يكن موجود)
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('users') AND name = 'IX_users_username')
BEGIN
    CREATE UNIQUE INDEX IX_users_username ON users(username);
    PRINT '✅ تم إنشاء فهرس فريد لاسم المستخدم';
END
ELSE
BEGIN
    PRINT '⚠️ فهرس اسم المستخدم موجود بالفعل';
END

-- 10. التحقق من النتائج وعرض ملخص التحديث
PRINT '';
PRINT '📊 ملخص التحديث...';
PRINT 'Update Summary...';
PRINT '';

SELECT
    '✅ تم تحديث جدول المستخدمين بنجاح' AS Status,
    COUNT(*) AS TotalUsers,
    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) AS ActiveUsers,
    SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) AS InactiveUsers,
    SUM(CASE WHEN email IS NOT NULL AND email != '' AND email NOT LIKE '%@temp.local' THEN 1 ELSE 0 END) AS UsersWithRealEmail,
    SUM(CASE WHEN email LIKE '%@temp.local' THEN 1 ELSE 0 END) AS UsersWithTempEmail,
    SUM(CASE WHEN full_name IS NOT NULL AND full_name != '' THEN 1 ELSE 0 END) AS UsersWithFullName,
    SUM(CASE WHEN phone_number IS NOT NULL AND phone_number != '' THEN 1 ELSE 0 END) AS UsersWithPhone,
    SUM(CASE WHEN last_login_at IS NOT NULL THEN 1 ELSE 0 END) AS UsersWithLoginHistory
FROM users;

PRINT '';
PRINT '============================================================================';
PRINT '🎉 تم تحديث جدول المستخدمين بنجاح لدعم نظام المصادقة الجديد';
PRINT '🎉 Users table updated successfully for new authentication system';
PRINT '';
PRINT '📝 ملاحظات مهمة:';
PRINT '📝 Important Notes:';
PRINT '• المستخدمون الذين لم يكن لديهم بريد إلكتروني حصلوا على بريد مؤقت';
PRINT '• Users without email got temporary email addresses';
PRINT '• يمكن للمستخدمين تحديث بياناتهم من خلال النظام';
PRINT '• Users can update their information through the system';
PRINT '• جميع المستخدمين الموجودين تم تفعيلهم افتراضياً';
PRINT '• All existing users are activated by default';
PRINT '============================================================================';
