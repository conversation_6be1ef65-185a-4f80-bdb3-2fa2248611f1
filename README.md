# نظام إدارة المشتريات والمخزون
## Electronics Store Management System

نظام شامل لإدارة المشتريات والمخزون للمتاجر الإلكترونية باستخدام ASP.NET Core و Angular مع Clean Architecture.

## الهيكلة العامة للمشروع

```
ElectronicsStoreSystem/
├── Backend/                          # ASP.NET Core Web API
│   ├── ElectronicsStore.Domain/      # Domain Layer
│   ├── ElectronicsStore.Application/ # Application Layer  
│   ├── ElectronicsStore.Infrastructure/ # Infrastructure Layer
│   └── ElectronicsStore.WebAPI/      # Web API Layer
├── Frontend/                         # Angular Application
│   └── electronics-store-app/
└── Database/                         # Database Scripts
    └── DatabaseSchema.sql
```

## المتطلبات التقنية

### Backend
- ASP.NET Core 8.0
- Entity Framework Core
- SQL Server
- Clean Architecture Pattern

### Frontend  
- Angular 17+
- TypeScript
- Angular Material UI
- RxJS

## قاعدة البيانات

النظام يدعم الجداول التالية:
- Categories (الأصناف)
- Suppliers (الموردين) 
- Products (المنتجات)
- Users & Roles (المستخدمين والأدوار)
- Purchase Invoices (فواتير الشراء)
- Sales Invoices (فواتير البيع)
- Inventory Management (إدارة المخزون)
- Returns Management (إدارة المرتجعات)

## الميزات الرئيسية

1. **إدارة المنتجات والأصناف**
2. **إدارة الموردين**
3. **فواتير الشراء والبيع**
4. **تتبع المخزون الفوري**
5. **إدارة المرتجعات**
6. **نظام الصلاحيات والأدوار**
7. **التقارير والإحصائيات**

## كيفية التشغيل

### Backend
```bash
cd Backend/ElectronicsStore.WebAPI
dotnet run
```

### Frontend
```bash
cd Frontend/electronics-store-app
ng serve
```

## المطورين

تم تطوير هذا النظام باستخدام Clean Architecture لضمان قابلية الصيانة والتوسع.
