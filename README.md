# نظام إدارة المشتريات والمخزون
## Electronics Store Management System

نظام شامل لإدارة المشتريات والمخزون للمتاجر الإلكترونية باستخدام ASP.NET Core و Angular مع Clean Architecture.

## الهيكلة العامة للمشروع

```
ElectronicsStoreSystem/
├── Backend/                          # ASP.NET Core Web API
│   ├         
│   ├── ElectronicsStore.Domain/      # Domain Layer
│   ├── ElectronicsStore.Application/ # Application Layer  
│   ├── ElectronicsStore.Infrastructure/ # Infrastructure Layer
│   └── ElectronicsStore.WebAPI/      # Web API Layer
│
├── Frontend/                         # Angular Application
│   ├
│   └── electronics-store-app/
|
└── Database/                         # Database Scripts
    └── DatabaseSchema.sql
```

## المتطلبات التقنية

### Backend
- ASP.NET Core 8.0
- Entity Framework Core
- SQL Server
- Clean Architecture Pattern

### Frontend  
- Angular 17+
- TypeScript
- Angular Material UI
- RxJS

## قاعدة البيانات

النظام يدعم الجداول التالية:
- Categories (الأصناف)
- Suppliers (الموردين) 
- Products (المنتجات)
- Users & Roles (المستخدمين والأدوار)
- Purchase Invoices (فواتير الشراء)
- Sales Invoices (فواتير البيع)
- Inventory Management (إدارة المخزون)
- Returns Management (إدارة المرتجعات)

## الميزات الرئيسية

1. **إدارة المنتجات والأصناف**
2. **إدارة الموردين**
3. **فواتير الشراء والبيع**
4. **تتبع المخزون الفوري**
5. **إدارة المرتجعات**
6. **نظام الصلاحيات والأدوار**
7. **التقارير والإحصائيات**

## كيفية التشغيل

### الطريقة السريعة
```bash
# تشغيل الملف التلقائي
run-system.bat
```

### الطريقة اليدوية

#### 1. إعداد قاعدة البيانات
```sql
-- في SQL Server Management Studio
-- تشغيل الملفات بالترتيب:
Database/DatabaseSchema.sql
Database/DatabaseSchema_Part2.sql
Database/DatabaseViews.sql
```

#### 2. تشغيل Backend
```bash
cd Backend
dotnet restore
dotnet run --project ElectronicsStore.WebAPI
```

#### 3. تشغيل Frontend
```bash
cd Frontend
npm install
npm start
```

### الروابط
- **Backend API**: https://localhost:7000
- **Frontend App**: http://localhost:4200
- **Swagger Documentation**: https://localhost:7000/swagger

## الهيكلة التفصيلية

### Backend (Clean Architecture)
```
Backend/
├── ElectronicsStore.Domain/          # Domain Layer
│   ├── Entities/                     # Domain Entities
│   ├── Enums/                        # Domain Enums
│   ├── ValueObjects/                 # Value Objects
│   └── Interfaces/                   # Repository Interfaces
├── ElectronicsStore.Application/     # Application Layer
│   ├── DTOs/                         # Data Transfer Objects
│   ├── Services/                     # Application Services
│   ├── Interfaces/                   # Service Interfaces
│   └── UseCases/                     # Use Cases
├── ElectronicsStore.Infrastructure/  # Infrastructure Layer
│   ├── Data/                         # DbContext
│   ├── Repositories/                 # Repository Implementations
│   └── Configurations/               # EF Configurations
└── ElectronicsStore.WebAPI/          # Presentation Layer
    ├── Controllers/                  # API Controllers
    └── Program.cs                    # Application Entry Point
```

### Frontend (Angular)
```
Frontend/
├── src/
│   ├── app/
│   │   ├── components/               # Angular Components
│   │   │   ├── dashboard/            # لوحة التحكم
│   │   │   ├── categories/           # إدارة الأصناف
│   │   │   ├── products/             # إدارة المنتجات
│   │   │   ├── suppliers/            # إدارة الموردين
│   │   │   ├── purchase-invoices/    # فواتير الشراء
│   │   │   ├── sales-invoices/       # فواتير البيع
│   │   │   └── inventory/            # إدارة المخزون
│   │   ├── services/                 # Angular Services
│   │   ├── models/                   # TypeScript Models
│   │   └── app.component.ts          # Root Component
│   └── styles.scss                   # Global Styles
├── package.json                      # Dependencies
└── angular.json                      # Angular Configuration
```

## المطورين

تم تطوير هذا النظام باستخدام:
- **Clean Architecture** لضمان قابلية الصيانة والتوسع
- **Domain-Driven Design (DDD)** لتنظيم منطق الأعمال
- **Repository Pattern** لفصل طبقة الوصول للبيانات
- **Angular Material** لواجهة المستخدم
- **Entity Framework Core** لإدارة قاعدة البيانات
