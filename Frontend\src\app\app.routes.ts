import { Routes } from '@angular/router';

export const routes: Routes = [
  { path: '', redirectTo: '/dashboard', pathMatch: 'full' },
  { 
    path: 'dashboard', 
    loadComponent: () => import('./components/dashboard/dashboard.component').then(m => m.DashboardComponent)
  },
  { 
    path: 'categories', 
    loadComponent: () => import('./components/categories/categories.component').then(m => m.CategoriesComponent)
  },
  { 
    path: 'products', 
    loadComponent: () => import('./components/products/products.component').then(m => m.ProductsComponent)
  },
  { 
    path: 'suppliers', 
    loadComponent: () => import('./components/suppliers/suppliers.component').then(m => m.SuppliersComponent)
  },
  { 
    path: 'purchase-invoices', 
    loadComponent: () => import('./components/purchase-invoices/purchase-invoices.component').then(m => m.PurchaseInvoicesComponent)
  },
  { 
    path: 'sales-invoices', 
    loadComponent: () => import('./components/sales-invoices/sales-invoices.component').then(m => m.SalesInvoicesComponent)
  },
  { 
    path: 'inventory', 
    loadComponent: () => import('./components/inventory/inventory.component').then(m => m.InventoryComponent)
  }
];
