// ===== Products Management System =====

class ProductsManager {
    constructor() {
        this.apiService = new ApiService(); // استخدام نفس ApiService المحسن
        this.products = [];
        this.categories = [];
        this.suppliers = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.filters = {
            search: '',
            category: '',
            supplier: '',
            status: ''
        };

        this.init();
    }

    // Initialize the products manager
    init() {
        console.log('🚀 Products Manager Initialized');
        this.setupEventListeners();
        this.loadInitialData();
    }

    // Setup event listeners
    setupEventListeners() {
        // Search input
        const searchInput = document.getElementById('productSearch');
        if (searchInput) {
            console.log('✅ Search input found');
            searchInput.addEventListener('input', this.debounce((e) => {
                console.log('🔍 Search changed:', e.target.value);
                this.filters.search = e.target.value;
                this.loadProducts();
            }, 300));
        } else {
            console.log('❌ Search input not found');
        }

        // Filter dropdowns
        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter) {
            console.log('✅ Category filter found');
            categoryFilter.addEventListener('change', (e) => {
                console.log('📂 Category changed:', e.target.value);
                this.filters.category = e.target.value;
                this.loadProducts();
            });
        } else {
            console.log('❌ Category filter not found');
        }

        const supplierFilter = document.getElementById('supplierFilter');
        if (supplierFilter) {
            console.log('✅ Supplier filter found');
            supplierFilter.addEventListener('change', (e) => {
                console.log('🏪 Supplier changed:', e.target.value);
                this.filters.supplier = e.target.value;
                this.loadProducts();
            });
        } else {
            console.log('❌ Supplier filter not found');
        }

        const stockFilter = document.getElementById('stockFilter');
        if (stockFilter) {
            console.log('✅ Stock filter found');
            stockFilter.addEventListener('change', (e) => {
                console.log('📊 Stock status changed:', e.target.value);
                this.filters.status = e.target.value;
                this.loadProducts();
            });
        } else {
            console.log('❌ Stock filter not found');
        }

        console.log('🎯 Event listeners setup complete. Current filters:', this.filters);
    }

    // Reset all filters
    resetFilters() {
        console.log('🔄 Resetting all filters');

        this.filters = {
            search: '',
            category: '',
            supplier: '',
            status: ''
        };

        // Reset UI elements
        const searchInput = document.getElementById('productSearch');
        const categoryFilter = document.getElementById('categoryFilter');
        const supplierFilter = document.getElementById('supplierFilter');
        const stockFilter = document.getElementById('stockFilter');

        if (searchInput) searchInput.value = '';
        if (categoryFilter) categoryFilter.value = '';
        if (supplierFilter) supplierFilter.value = '';
        if (stockFilter) stockFilter.value = '';

        console.log('✅ Filters reset, reloading products');
        this.loadProducts();
    }

    // Load initial data
    async loadInitialData() {
        try {
            await Promise.all([
                this.loadProducts(),
                this.loadCategories(),
                this.loadSuppliers()
            ]);
        } catch (error) {
            console.error('Error loading initial data:', error);
            this.showNotification('خطأ في تحميل البيانات', 'error');
        }
    }

    // Load products from ASP.NET backend
    async loadProducts() {
        try {
            this.showLoading(true);

            let endpoint = '/products';

            // Apply backend filters first (category and supplier)
            if (this.filters.category) {
                endpoint = `/products/category/${this.filters.category}`;
            } else if (this.filters.supplier) {
                endpoint = `/products/supplier/${this.filters.supplier}`;
            }

            console.log('🔍 Loading products from:', endpoint);
            const products = await this.apiService.get(endpoint);
            let filteredProducts = Array.isArray(products) ? products : [];

            console.log('📦 Loaded products:', filteredProducts.length);

            // Apply frontend filters (search and stock status)
            filteredProducts = this.applyFrontendFilters(filteredProducts);

            this.products = filteredProducts;
            console.log('✅ Final filtered products:', this.products.length);

            this.renderProducts();
            this.updateProductsCount();

        } catch (error) {
            console.error('Error loading products:', error);
            this.showNotification('خطأ في تحميل المنتجات: ' + error.message, 'error');
            this.renderEmptyState();
        } finally {
            this.showLoading(false);
        }
    }

    // Apply frontend filters (search and stock status)
    applyFrontendFilters(products) {
        let filtered = [...products];

        // Apply search filter
        if (this.filters.search && this.filters.search.trim()) {
            const searchTerm = this.filters.search.toLowerCase().trim();
            console.log('🔍 Applying search filter:', searchTerm);

            filtered = filtered.filter(product => {
                const name = (product.name || '').toLowerCase();
                const barcode = (product.barcode || '').toLowerCase();
                const description = (product.description || '').toLowerCase();

                return name.includes(searchTerm) ||
                       barcode.includes(searchTerm) ||
                       description.includes(searchTerm);
            });

            console.log('📝 Search results:', filtered.length);
        }

        // Apply stock status filter
        if (this.filters.status) {
            console.log('📊 Applying stock filter:', this.filters.status);

            filtered = filtered.filter(product => {
                const quantity = product.currentQuantity || 0;
                const minStock = product.minStockLevel || 10;

                switch (this.filters.status) {
                    case 'available':
                        return quantity > minStock;
                    case 'low':
                        return quantity > 0 && quantity <= minStock;
                    case 'out':
                        return quantity === 0;
                    default:
                        return true;
                }
            });

            console.log('📊 Stock filter results:', filtered.length);
        }

        return filtered;
    }

    // Load categories from ASP.NET API
    async loadCategories() {
        try {
            this.categories = await this.apiService.get('/categories');
            this.renderCategoryFilter();
        } catch (error) {
            console.error('Error loading categories:', error);
        }
    }

    // Load suppliers from ASP.NET API
    async loadSuppliers() {
        try {
            this.suppliers = await this.apiService.get('/suppliers');
            this.renderSupplierFilter();
        } catch (error) {
            console.error('Error loading suppliers:', error);
        }
    }

    // Render products table
    renderProducts() {
        const tbody = document.getElementById('productsTableBody');
        if (!tbody) return;

        if (this.products.length === 0) {
            this.renderEmptyState();
            return;
        }

        tbody.innerHTML = this.products.map(product => `
            <tr class="hover:bg-gray-50" data-product-id="${product.id}">
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center space-x-3 space-x-reverse">
                        <div class="w-12 h-12 bg-gray-200 rounded-lg overflow-hidden">
                            <div class="w-full h-full bg-gray-300 flex items-center justify-center">
                                <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-gray-900">${product.name}</div>
                            <div class="text-sm text-gray-500">${product.description || ''}</div>
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${product.barcode || '-'}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${product.categoryName || '-'}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">${this.formatCurrency(product.defaultCostPrice)}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${this.formatCurrency(product.defaultSellingPrice)}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600">${this.formatCurrency(product.minSellingPrice)}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                    ${this.renderStockBadge(product.currentQuantity)}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${product.supplierName || '-'}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <button onclick="productsManager.editProduct(${product.id})"
                                class="text-primary-600 hover:text-primary-900 transition-colors">
                            تعديل
                        </button>
                        <button onclick="productsManager.deleteProduct(${product.id})"
                                class="text-red-600 hover:text-red-900 transition-colors">
                            حذف
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    // Render stock badge
    renderStockBadge(quantity, minStock = 10) {
        let badgeClass = 'bg-green-100 text-green-800';
        let text = `${quantity} قطعة`;

        if (quantity === 0) {
            badgeClass = 'bg-red-100 text-red-800';
            text = 'نفد المخزون';
        } else if (quantity <= minStock) {
            badgeClass = 'bg-yellow-100 text-yellow-800';
            text = `${quantity} قطعة - مخزون منخفض`;
        }

        return `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${badgeClass}">${text}</span>`;
    }

    // Update products count display
    updateProductsCount() {
        const countElement = document.querySelector('.products-count');
        if (countElement) {
            countElement.textContent = `${this.products.length} منتج`;
        }
    }

    // Show/hide loading state
    showLoading(show) {
        const tbody = document.getElementById('productsTableBody');
        const loadingRow = document.getElementById('loadingRow');

        if (show) {
            if (tbody && !loadingRow) {
                tbody.innerHTML = `
                    <tr id="loadingRow">
                        <td colspan="9" class="px-6 py-4 text-center text-gray-500">
                            <div class="flex items-center justify-center">
                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                جاري تحميل المنتجات...
                            </div>
                        </td>
                    </tr>
                `;
            }
        }
    }

    // Render empty state
    renderEmptyState() {
        const tbody = document.getElementById('productsTableBody');
        if (!tbody) return;

        tbody.innerHTML = `
            <tr>
                <td colspan="9" class="px-6 py-4 text-center text-gray-500">
                    <div class="flex flex-col items-center justify-center py-8">
                        <svg class="w-12 h-12 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                        <p class="text-lg font-medium text-gray-900 mb-2">لا توجد منتجات</p>
                        <p class="text-gray-500">لم يتم العثور على منتجات تطابق معايير البحث</p>
                    </div>
                </td>
            </tr>
        `;
    }

    // Format currency
    formatCurrency(amount) {
        if (!amount) return '0.00 ر.س';
        return parseFloat(amount).toFixed(2) + ' ر.س';
    }

    // Debounce function for search
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Show notification
    showNotification(message, type = 'info') {
        // Use the global showNotification function if available
        if (typeof showNotification === 'function') {
            showNotification(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    // Render empty state
    renderEmptyState() {
        const tbody = document.querySelector('#products-table-body');
        if (!tbody) return;

        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="px-6 py-12 text-center">
                    <div class="text-gray-500">
                        <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                        <p class="text-lg font-medium mb-2">لا توجد منتجات</p>
                        <p class="text-sm">ابدأ بإضافة منتجات جديدة لمتجرك</p>
                        <button onclick="productsManager.openAddProductModal()" 
                                class="mt-4 btn-primary text-white px-4 py-2 rounded-lg font-medium">
                            إضافة منتج جديد
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    // Render category filter
    renderCategoryFilter() {
        const categoryFilter = document.getElementById('categoryFilter');
        const productCategorySelect = document.getElementById('productCategory');

        const categoryOptions = `
            <option value="">جميع الفئات</option>
            ${this.categories.map(category =>
                `<option value="${category.id}">${category.name}</option>`
            ).join('')}
        `;

        if (categoryFilter) {
            categoryFilter.innerHTML = categoryOptions;
        }

        // Also update the product form category select
        if (productCategorySelect) {
            productCategorySelect.innerHTML = `
                <option value="">اختر الفئة</option>
                ${this.categories.map(category =>
                    `<option value="${category.id}">${category.name}</option>`
                ).join('')}
            `;
        }
    }

    // Render supplier filter
    renderSupplierFilter() {
        const supplierFilter = document.getElementById('supplierFilter');
        const productSupplierSelect = document.getElementById('productSupplier');

        const supplierOptions = `
            <option value="">جميع الموردين</option>
            ${this.suppliers.map(supplier =>
                `<option value="${supplier.id}">${supplier.name}</option>`
            ).join('')}
        `;

        if (supplierFilter) {
            supplierFilter.innerHTML = supplierOptions;
        }

        // Also update the product form supplier select
        if (productSupplierSelect) {
            productSupplierSelect.innerHTML = `
                <option value="">اختر المورد</option>
                ${this.suppliers.map(supplier =>
                    `<option value="${supplier.id}">${supplier.name}</option>`
                ).join('')}
            `;
        }
    }

    // Render pagination
    renderPagination(totalPages, currentPage, totalItems) {
        const paginationContainer = document.querySelector('#pagination-container');
        if (!paginationContainer) return;

        const startItem = ((currentPage - 1) * this.itemsPerPage) + 1;
        const endItem = Math.min(currentPage * this.itemsPerPage, totalItems);

        paginationContainer.innerHTML = `
            <div class="flex-1 flex justify-between sm:hidden">
                <button onclick="productsManager.goToPage(${currentPage - 1})" 
                        ${currentPage <= 1 ? 'disabled' : ''}
                        class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50">
                    السابق
                </button>
                <button onclick="productsManager.goToPage(${currentPage + 1})" 
                        ${currentPage >= totalPages ? 'disabled' : ''}
                        class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50">
                    التالي
                </button>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        عرض <span class="font-medium">${startItem}</span> إلى <span class="font-medium">${endItem}</span> من <span class="font-medium">${totalItems}</span> نتيجة
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                        ${this.renderPaginationButtons(totalPages, currentPage)}
                    </nav>
                </div>
            </div>
        `;
    }

    // Render pagination buttons
    renderPaginationButtons(totalPages, currentPage) {
        let buttons = '';
        
        // Previous button
        buttons += `
            <button onclick="productsManager.goToPage(${currentPage - 1})" 
                    ${currentPage <= 1 ? 'disabled' : ''}
                    class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50">
                السابق
            </button>
        `;

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === currentPage) {
                buttons += `
                    <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-primary-50 text-sm font-medium text-primary-600">
                        ${i}
                    </button>
                `;
            } else if (i === 1 || i === totalPages || (i >= currentPage - 1 && i <= currentPage + 1)) {
                buttons += `
                    <button onclick="productsManager.goToPage(${i})" 
                            class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                        ${i}
                    </button>
                `;
            } else if (i === currentPage - 2 || i === currentPage + 2) {
                buttons += `
                    <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                        ...
                    </span>
                `;
            }
        }

        // Next button
        buttons += `
            <button onclick="productsManager.goToPage(${currentPage + 1})" 
                    ${currentPage >= totalPages ? 'disabled' : ''}
                    class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50">
                التالي
            </button>
        `;

        return buttons;
    }

    // Go to specific page
    goToPage(page) {
        if (page < 1 || page > Math.ceil(this.products.length / this.itemsPerPage)) return;
        this.currentPage = page;
        this.loadProducts();
    }

    // Utility functions
    formatCurrency(amount) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(amount);
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    showLoading(show) {
        const loadingElement = document.querySelector('#products-loading');
        if (loadingElement) {
            loadingElement.style.display = show ? 'flex' : 'none';
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300 translate-x-full`;

        // Set notification style based on type
        switch (type) {
            case 'success':
                notification.classList.add('bg-green-500', 'text-white');
                break;
            case 'error':
                notification.classList.add('bg-red-500', 'text-white');
                break;
            case 'warning':
                notification.classList.add('bg-yellow-500', 'text-white');
                break;
            default:
                notification.classList.add('bg-blue-500', 'text-white');
        }

        notification.innerHTML = `
            <div class="flex items-center justify-between">
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="mr-2 text-white hover:text-gray-200">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, 5000);
    }

    // Open add product modal
    openAddProductModal() {
        this.openProductModal();
    }

    // Edit product
    async editProduct(productId) {
        try {
            const product = await this.apiService.get(`/products/${productId}`);
            this.openProductModal(product);
        } catch (error) {
            console.error('Error fetching product:', error);
            this.showNotification('خطأ في تحميل بيانات المنتج: ' + error.message, 'error');
        }
    }

    // Delete product
    async deleteProduct(productId) {
        if (!confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
            return;
        }

        try {
            const result = await this.apiService.delete(`/products/${productId}`);
            console.log('Delete result:', result);
            this.showNotification('تم حذف المنتج بنجاح', 'success');
            this.loadProducts();
        } catch (error) {
            console.error('Error deleting product:', error);
            this.showNotification('خطأ في حذف المنتج: ' + error.message, 'error');
        }
    }

    // Open product modal (add/edit)
    openProductModal(product = null) {
        const modal = document.getElementById('productModal');
        if (!modal) {
            this.createProductModal();
            return;
        }

        // Fill form if editing
        if (product) {
            const productIdField = document.getElementById('productId');
            const productNameField = document.getElementById('productName');
            const productBarcodeField = document.getElementById('productBarcode');
            const productDescriptionField = document.getElementById('productDescription');
            const productPurchasePriceField = document.getElementById('productPurchasePrice');
            const productSalePriceField = document.getElementById('productSalePrice');
            const productMinPriceField = document.getElementById('productMinPrice');
            const productQuantityField = document.getElementById('productQuantity');
            const productMinStockField = document.getElementById('productMinStock');
            const productCategoryField = document.getElementById('productCategory');
            const productSupplierField = document.getElementById('productSupplier');
            const modalTitle = document.getElementById('productModalTitle');
            const submitBtn = document.getElementById('productSubmitBtn');

            if (productIdField) productIdField.value = product.id;
            if (productNameField) productNameField.value = product.name;
            if (productBarcodeField) productBarcodeField.value = product.barcode || '';
            if (productDescriptionField) productDescriptionField.value = product.description || '';
            if (productPurchasePriceField) productPurchasePriceField.value = product.defaultCostPrice || '';
            if (productSalePriceField) productSalePriceField.value = product.defaultSellingPrice;
            if (productMinPriceField) productMinPriceField.value = product.minSellingPrice;
            if (productQuantityField) productQuantityField.value = product.currentQuantity || 0;
            if (productMinStockField) productMinStockField.value = product.minStockLevel || '';
            if (productCategoryField) productCategoryField.value = product.categoryId || '';
            if (productSupplierField) productSupplierField.value = product.supplierId || '';

            if (modalTitle) modalTitle.textContent = 'تعديل المنتج';
            if (submitBtn) submitBtn.textContent = 'تحديث المنتج';
        } else {
            const productForm = document.getElementById('productForm');
            const productIdField = document.getElementById('productId');
            const modalTitle = document.getElementById('productModalTitle');
            const submitBtn = document.getElementById('productSubmitBtn');

            if (productForm) productForm.reset();
            if (productIdField) productIdField.value = '';
            if (modalTitle) modalTitle.textContent = 'إضافة منتج جديد';
            if (submitBtn) submitBtn.textContent = 'إضافة المنتج';
        }

        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    // Close product modal
    closeProductModal() {
        const modal = document.getElementById('productModal');
        if (modal) {
            modal.classList.remove('active');
            document.body.style.overflow = '';
        }
    }

    // Create product modal
    createProductModal() {
        const modalHTML = `
            <div id="productModal" class="modal">
                <div class="modal-content max-w-2xl">
                    <div class="flex items-center justify-between mb-6">
                        <h2 id="modalTitle" class="text-xl font-bold text-gray-900">إضافة منتج جديد</h2>
                        <button onclick="this.closest('.modal').classList.remove('active'); document.body.style.overflow = '';"
                                class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <form id="productForm" onsubmit="productsManager.saveProduct(event)">
                        <input type="hidden" id="productId">

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="productName" class="block text-sm font-medium text-gray-700 mb-2">اسم المنتج *</label>
                                <input type="text" id="productName" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            </div>

                            <div>
                                <label for="productBarcode" class="block text-sm font-medium text-gray-700 mb-2">الباركود</label>
                                <input type="text" id="productBarcode"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            </div>

                            <div class="md:col-span-2">
                                <label for="productDescription" class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                                <textarea id="productDescription" rows="3"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"></textarea>
                            </div>

                            <div>
                                <label for="productCostPrice" class="block text-sm font-medium text-gray-700 mb-2">سعر التكلفة *</label>
                                <input type="number" id="productCostPrice" step="0.01" min="0" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            </div>

                            <div>
                                <label for="productSellingPrice" class="block text-sm font-medium text-gray-700 mb-2">سعر البيع *</label>
                                <input type="number" id="productSellingPrice" step="0.01" min="0" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            </div>

                            <div>
                                <label for="productMinPrice" class="block text-sm font-medium text-gray-700 mb-2">أقل سعر بيع *</label>
                                <input type="number" id="productMinPrice" step="0.01" min="0" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            </div>

                            <div>
                                <label for="productCategory" class="block text-sm font-medium text-gray-700 mb-2">الفئة</label>
                                <select id="productCategory"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                                    <option value="">اختر الفئة</option>
                                </select>
                            </div>

                            <div>
                                <label for="productSupplier" class="block text-sm font-medium text-gray-700 mb-2">المورد</label>
                                <select id="productSupplier"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                                    <option value="">اختر المورد</option>
                                </select>
                            </div>

                            <div class="md:col-span-2">
                                <label for="productImage" class="block text-sm font-medium text-gray-700 mb-2">صورة المنتج</label>
                                <input type="file" id="productImage" accept="image/*"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            </div>
                        </div>

                        <div class="flex justify-end space-x-3 space-x-reverse mt-6">
                            <button type="button" onclick="this.closest('.modal').classList.remove('active'); document.body.style.overflow = '';"
                                    class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                                إلغاء
                            </button>
                            <button type="submit" id="submitBtn"
                                    class="px-4 py-2 btn-primary text-white rounded-md">
                                إضافة المنتج
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Populate dropdowns
        this.populateModalDropdowns();

        // Open modal
        setTimeout(() => this.openProductModal(), 100);
    }

    // Populate modal dropdowns
    populateModalDropdowns() {
        const categorySelect = document.getElementById('productCategory');
        const supplierSelect = document.getElementById('productSupplier');

        if (categorySelect && this.categories.length > 0) {
            categorySelect.innerHTML = `
                <option value="">اختر الفئة</option>
                ${this.categories.map(category =>
                    `<option value="${category.id}">${category.name}</option>`
                ).join('')}
            `;
        }

        if (supplierSelect && this.suppliers.length > 0) {
            supplierSelect.innerHTML = `
                <option value="">اختر المورد</option>
                ${this.suppliers.map(supplier =>
                    `<option value="${supplier.id}">${supplier.name}</option>`
                ).join('')}
            `;
        }
    }

    // Save product (add/edit)
    async saveProduct(event) {
        event.preventDefault();

        const productId = document.getElementById('productId').value;

        // Prepare data for ASP.NET API
        const productData = {
            name: document.getElementById('productName').value,
            description: document.getElementById('productDescription').value || null,
            barcode: document.getElementById('productBarcode').value || null,
            defaultCostPrice: parseFloat(document.getElementById('productPurchasePrice').value),
            defaultSellingPrice: parseFloat(document.getElementById('productSalePrice').value),
            minSellingPrice: parseFloat(document.getElementById('productMinPrice').value),
            categoryId: parseInt(document.getElementById('productCategory').value),
            supplierId: document.getElementById('productSupplier').value ? parseInt(document.getElementById('productSupplier').value) : null
        };

        // Add quantity if provided
        const quantityField = document.getElementById('productQuantity');
        if (quantityField && quantityField.value) {
            productData.initialQuantity = parseInt(quantityField.value);
        }

        // Add min stock if provided
        const minStockField = document.getElementById('productMinStock');
        if (minStockField && minStockField.value) {
            productData.minStockLevel = parseInt(minStockField.value);
        }

        // Add ID for update
        if (productId) {
            productData.id = parseInt(productId);
        }

        try {
            let result;
            if (productId) {
                result = await this.apiService.put(`/products/${productId}`, productData);
            } else {
                result = await this.apiService.post('/products', productData);
            }

            console.log('Save result:', result);
            const message = productId ? 'تم تحديث المنتج بنجاح' : 'تم إضافة المنتج بنجاح';
            this.showNotification(message, 'success');

            // Close modal and refresh products
            document.getElementById('productModal').classList.remove('active');
            document.body.style.overflow = '';
            this.loadProducts();
        } catch (error) {
            console.error('Error saving product:', error);
            this.showNotification('خطأ في حفظ المنتج: ' + error.message, 'error');
        }
    }
}

// Initialize products manager when DOM is loaded
let productsManager;
document.addEventListener('DOMContentLoaded', () => {
    productsManager = new ProductsManager();
});
