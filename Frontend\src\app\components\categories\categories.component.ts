import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snackbar';
import { CategoryService } from '../../services/category.service';
import { Category } from '../../models/category.model';

@Component({
  selector: 'app-categories',
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatDialogModule,
    MatSnackBarModule
  ],
  template: `
    <div class="categories-container">
      <div class="header">
        <h1>إدارة الأصناف</h1>
        <button mat-raised-button color="primary" (click)="openAddDialog()">
          <mat-icon>add</mat-icon>
          إضافة صنف جديد
        </button>
      </div>

      <div class="table-container">
        <table mat-table [dataSource]="categories" class="categories-table">
          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef>الرقم</th>
            <td mat-cell *matCellDef="let category">{{ category.id }}</td>
          </ng-container>

          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef>اسم الصنف</th>
            <td mat-cell *matCellDef="let category">{{ category.name }}</td>
          </ng-container>

          <ng-container matColumnDef="createdAt">
            <th mat-header-cell *matHeaderCellDef>تاريخ الإنشاء</th>
            <td mat-cell *matCellDef="let category">{{ category.createdAt | date:'short' }}</td>
          </ng-container>

          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>الإجراءات</th>
            <td mat-cell *matCellDef="let category">
              <button mat-icon-button color="primary" (click)="editCategory(category)">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button color="warn" (click)="deleteCategory(category)">
                <mat-icon>delete</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
      </div>
    </div>
  `,
  styles: [`
    .categories-container {
      padding: 20px;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .table-container {
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .categories-table {
      width: 100%;
    }

    .mat-mdc-header-cell {
      font-weight: bold;
      background-color: #f5f5f5;
    }
  `]
})
export class CategoriesComponent implements OnInit {
  categories: Category[] = [];
  displayedColumns: string[] = ['id', 'name', 'createdAt', 'actions'];

  constructor(
    private categoryService: CategoryService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit() {
    this.loadCategories();
  }

  loadCategories() {
    this.categoryService.getCategories().subscribe({
      next: (categories) => {
        this.categories = categories;
      },
      error: (error) => {
        this.snackBar.open('خطأ في تحميل الأصناف', 'إغلاق', { duration: 3000 });
        console.error('Error loading categories:', error);
      }
    });
  }

  openAddDialog() {
    // TODO: Implement add category dialog
    this.snackBar.open('سيتم إضافة نافذة إضافة الصنف قريباً', 'إغلاق', { duration: 3000 });
  }

  editCategory(category: Category) {
    // TODO: Implement edit category dialog
    this.snackBar.open('سيتم إضافة نافذة تعديل الصنف قريباً', 'إغلاق', { duration: 3000 });
  }

  deleteCategory(category: Category) {
    if (confirm(`هل أنت متأكد من حذف الصنف "${category.name}"؟`)) {
      this.categoryService.deleteCategory(category.id).subscribe({
        next: () => {
          this.snackBar.open('تم حذف الصنف بنجاح', 'إغلاق', { duration: 3000 });
          this.loadCategories();
        },
        error: (error) => {
          this.snackBar.open('خطأ في حذف الصنف', 'إغلاق', { duration: 3000 });
          console.error('Error deleting category:', error);
        }
      });
    }
  }
}
