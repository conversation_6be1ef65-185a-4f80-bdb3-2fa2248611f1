/**
 * Point of Sale (POS) Management System
 * Handles sales transactions, cart management, and payment processing
 */
class POSManager {
    constructor() {
        this.apiService = new ApiService();
        this.cart = [];
        this.products = [];
        this.categories = [];
        this.currentCustomer = null;
        this.discount = { type: 'none', value: 0 };
        this.tax = 0.15; // 15% VAT
        
        this.init();
    }

    // Initialize POS system
    init() {
        console.log('🛒 POS Manager Initialized');
        this.setupEventListeners();
        this.loadInitialData();
        this.updateCartDisplay();
    }

    // Setup event listeners
    setupEventListeners() {
        // Product search
        const productSearch = document.getElementById('productSearch');
        if (productSearch) {
            productSearch.addEventListener('input', this.debounce((e) => {
                this.searchProducts(e.target.value);
            }, 300));
        }

        // Category filter
        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', (e) => {
                this.filterByCategory(e.target.value);
            });
        }

        // Barcode scanner
        const barcodeInput = document.getElementById('barcodeInput');
        if (barcodeInput) {
            barcodeInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.addProductByBarcode(e.target.value);
                    e.target.value = '';
                }
            });
        }

        // Discount inputs
        const discountPercent = document.getElementById('discountPercent');
        const discountAmount = document.getElementById('discountAmount');
        
        if (discountPercent) {
            discountPercent.addEventListener('input', (e) => {
                this.applyDiscount('percent', parseFloat(e.target.value) || 0);
            });
        }
        
        if (discountAmount) {
            discountAmount.addEventListener('input', (e) => {
                this.applyDiscount('amount', parseFloat(e.target.value) || 0);
            });
        }

        // Payment method buttons
        document.querySelectorAll('.payment-method').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.selectPaymentMethod(e.target.dataset.method);
            });
        });

        console.log('🎯 POS event listeners setup complete');
    }

    // Load initial data
    async loadInitialData() {
        console.log('📊 Loading POS initial data...');
        await Promise.all([
            this.loadProducts(),
            this.loadCategories()
        ]);
    }

    // Load products for POS
    async loadProducts() {
        try {
            const products = await this.apiService.get('/products');
            this.products = products.filter(p => (p.currentQuantity || 0) > 0); // Only available products
            this.renderProducts();
            console.log('📦 Loaded available products:', this.products.length);
        } catch (error) {
            console.error('Error loading products:', error);
            this.showNotification('خطأ في تحميل المنتجات', 'error');
        }
    }

    // Load categories
    async loadCategories() {
        try {
            this.categories = await this.apiService.get('/categories');
            this.renderCategoryFilter();
            console.log('📂 Loaded categories:', this.categories.length);
        } catch (error) {
            console.error('Error loading categories:', error);
        }
    }

    // Render products grid
    renderProducts(productsToRender = null) {
        const productsGrid = document.getElementById('productsGrid');
        if (!productsGrid) return;

        const products = productsToRender || this.products;

        if (products.length === 0) {
            productsGrid.innerHTML = `
                <div class="col-span-full text-center py-8">
                    <p class="text-gray-500">لا توجد منتجات متاحة</p>
                </div>
            `;
            return;
        }

        productsGrid.innerHTML = products.map(product => `
            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer ${(product.currentQuantity || 0) === 0 ? 'opacity-50' : ''}"
                 onclick="window.posManager.addToCart('${product.id}', '${product.name}', ${product.defaultSellingPrice}, ${product.currentQuantity || 0})">
                <div class="w-full h-24 bg-gray-200 rounded-lg mb-3 flex items-center justify-center overflow-hidden">
                    ${product.imageUrl ? 
                        `<img src="${product.imageUrl}" alt="${product.name}" class="w-full h-full object-cover">` :
                        `<svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>`
                    }
                </div>
                <h4 class="font-medium text-sm text-gray-900 mb-1">${product.name}</h4>
                <p class="text-xs text-gray-500 mb-2">${product.description || ''}</p>
                <p class="font-bold text-primary-600">${this.formatCurrency(product.defaultSellingPrice)}</p>
                <p class="text-xs ${(product.currentQuantity || 0) > 10 ? 'text-green-600' : (product.currentQuantity || 0) > 0 ? 'text-orange-600' : 'text-red-600'}">
                    ${(product.currentQuantity || 0) > 0 ? `متوفر: ${product.currentQuantity}` : 'نفد المخزون'}
                </p>
            </div>
        `).join('');
    }

    // Render category filter
    renderCategoryFilter() {
        const categoryFilter = document.getElementById('categoryFilter');
        if (!categoryFilter) return;

        categoryFilter.innerHTML = `
            <option value="">جميع الفئات</option>
            ${this.categories.map(category => 
                `<option value="${category.id}">${category.name}</option>`
            ).join('')}
        `;
    }

    // Search products
    searchProducts(searchTerm) {
        if (!searchTerm.trim()) {
            this.renderProducts();
            return;
        }

        const filteredProducts = this.products.filter(product => 
            product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (product.barcode && product.barcode.includes(searchTerm))
        );

        this.renderProducts(filteredProducts);
    }

    // Filter by category
    filterByCategory(categoryId) {
        if (!categoryId) {
            this.renderProducts();
            return;
        }

        const filteredProducts = this.products.filter(product => 
            product.categoryId == categoryId
        );

        this.renderProducts(filteredProducts);
    }

    // Add product by barcode
    async addProductByBarcode(barcode) {
        if (!barcode.trim()) return;

        try {
            const product = await this.apiService.get(`/products/barcode/${barcode}`);
            if (product && (product.currentQuantity || 0) > 0) {
                this.addToCart(product.id, product.name, product.defaultSellingPrice, product.currentQuantity);
                this.showNotification(`تم إضافة ${product.name} للسلة`, 'success');
            } else {
                this.showNotification('المنتج غير متوفر أو نفد المخزون', 'error');
            }
        } catch (error) {
            console.error('Error finding product by barcode:', error);
            this.showNotification('لم يتم العثور على المنتج', 'error');
        }
    }

    // Add item to cart
    addToCart(productId, name, price, availableQuantity) {
        console.log('🛒 Adding to cart:', { productId, name, price, availableQuantity });

        if (availableQuantity <= 0) {
            this.showNotification('المنتج غير متوفر', 'error');
            return;
        }

        const existingItem = this.cart.find(item => item.productId == productId);

        if (existingItem) {
            if (existingItem.quantity < availableQuantity) {
                existingItem.quantity += 1;
                existingItem.total = existingItem.quantity * existingItem.price;
                console.log('📦 Updated existing item:', existingItem);
            } else {
                this.showNotification('لا يمكن إضافة كمية أكثر من المتوفر', 'warning');
                return;
            }
        } else {
            const newItem = {
                productId: productId,
                name: name,
                price: parseFloat(price),
                quantity: 1,
                total: parseFloat(price),
                availableQuantity: availableQuantity
            };
            this.cart.push(newItem);
            console.log('✅ Added new item to cart:', newItem);
        }

        console.log('🛒 Current cart:', this.cart);
        this.updateCartDisplay();
        this.updateTotals();
        this.showNotification(`تم إضافة ${name} للسلة`, 'success');
    }

    // Remove item from cart
    removeFromCart(productId) {
        console.log('🗑️ Removing from cart:', productId);
        const itemToRemove = this.cart.find(item => item.productId == productId);
        if (itemToRemove) {
            console.log('✅ Found item to remove:', itemToRemove);
        }

        this.cart = this.cart.filter(item => item.productId != productId);
        console.log('🛒 Cart after removal:', this.cart);

        this.updateCartDisplay();
        this.updateTotals();
        this.showNotification('تم حذف المنتج من السلة', 'info');
    }

    // Update item quantity
    updateQuantity(productId, newQuantity) {
        console.log('🔢 Updating quantity for product:', productId, 'to:', newQuantity);
        const item = this.cart.find(item => item.productId == productId);
        if (!item) {
            console.log('❌ Item not found in cart');
            return;
        }

        if (newQuantity <= 0) {
            console.log('🗑️ Removing item from cart');
            this.removeFromCart(productId);
            return;
        }

        if (newQuantity > item.availableQuantity) {
            this.showNotification('الكمية المطلوبة أكبر من المتوفر', 'warning');
            return;
        }

        item.quantity = newQuantity;
        item.total = item.quantity * item.price;
        console.log('✅ Updated item:', item);

        this.updateCartDisplay();
        this.updateTotals();
    }

    // Update cart display
    updateCartDisplay() {
        console.log('🔄 Updating cart display, cart length:', this.cart.length);
        const cartItems = document.getElementById('cart-items');
        if (!cartItems) {
            console.error('❌ cartItems element not found');
            return;
        }

        if (this.cart.length === 0) {
            cartItems.innerHTML = `
                <div class="text-center text-gray-500 py-8">
                    <svg class="w-12 h-12 mx-auto mb-3 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m6 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"></path>
                    </svg>
                    <p>السلة فارغة</p>
                    <p class="text-sm">اختر المنتجات لإضافتها</p>
                </div>
            `;
            return;
        }

        const cartHTML = this.cart.map(item => `
            <div class="flex items-center justify-between py-2 border-b border-gray-100">
                <div class="flex-1">
                    <h4 class="text-sm font-medium text-gray-900">${item.name}</h4>
                    <p class="text-xs text-gray-500">${this.formatCurrency(item.price)} × ${item.quantity}</p>
                </div>
                <div class="flex items-center space-x-2 space-x-reverse">
                    <button onclick="window.posManager.updateQuantity('${item.productId}', ${item.quantity - 1})"
                            class="w-5 h-5 rounded bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 text-xs">
                        -
                    </button>
                    <span class="w-6 text-center text-xs font-medium">${item.quantity}</span>
                    <button onclick="window.posManager.updateQuantity('${item.productId}', ${item.quantity + 1})"
                            class="w-5 h-5 rounded bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 text-xs">
                        +
                    </button>
                    <button onclick="window.posManager.removeFromCart('${item.productId}')"
                            class="w-5 h-5 rounded bg-red-100 flex items-center justify-center text-red-600 hover:bg-red-200 text-xs ml-1">
                        ×
                    </button>
                </div>
                <div class="text-right ml-2">
                    <p class="text-sm font-medium text-gray-900">${this.formatCurrency(item.total)}</p>
                </div>
            </div>
        `).join('');

        cartItems.innerHTML = cartHTML;
        console.log('✅ Cart display updated with', this.cart.length, 'items');
    }

    // Apply discount
    applyDiscount(type, value) {
        this.discount = { type, value };
        
        // Clear the other discount input
        if (type === 'percent') {
            const discountAmount = document.getElementById('discountAmount');
            if (discountAmount) discountAmount.value = '';
        } else if (type === 'amount') {
            const discountPercent = document.getElementById('discountPercent');
            if (discountPercent) discountPercent.value = '';
        }
        
        this.updateTotals();
    }

    // Update totals
    updateTotals() {
        const subtotal = this.cart.reduce((sum, item) => sum + item.total, 0);
        
        let discountAmount = 0;
        if (this.discount.type === 'percent') {
            discountAmount = subtotal * (this.discount.value / 100);
        } else if (this.discount.type === 'amount') {
            discountAmount = this.discount.value;
        }
        
        const afterDiscount = subtotal - discountAmount;
        const taxAmount = afterDiscount * this.tax;
        const total = afterDiscount + taxAmount;

        // Update display
        const subtotalElement = document.getElementById('subtotal');
        const discountElement = document.getElementById('discount');
        const taxElement = document.getElementById('tax');
        const totalElement = document.getElementById('total');

        if (subtotalElement) subtotalElement.textContent = this.formatCurrency(subtotal);
        if (discountElement) discountElement.textContent = this.formatCurrency(discountAmount);
        if (taxElement) taxElement.textContent = this.formatCurrency(taxAmount);
        if (totalElement) totalElement.textContent = this.formatCurrency(total);

        // Update cart count
        const cartCount = document.getElementById('cart-count');
        if (cartCount) {
            cartCount.textContent = this.cart.reduce((sum, item) => sum + item.quantity, 0);
        }

        console.log('💰 Totals updated - Subtotal:', subtotal, 'Total:', total);
    }

    // Process payment
    async processPayment(paymentMethod) {
        if (this.cart.length === 0) {
            this.showNotification('السلة فارغة', 'error');
            return;
        }

        try {
            const subtotal = this.cart.reduce((sum, item) => sum + item.total, 0);
            let discountAmount = 0;
            
            if (this.discount.type === 'percent') {
                discountAmount = subtotal * (this.discount.value / 100);
            } else if (this.discount.type === 'amount') {
                discountAmount = this.discount.value;
            }
            
            const afterDiscount = subtotal - discountAmount;
            const taxAmount = afterDiscount * this.tax;
            const total = afterDiscount + taxAmount;

            const saleData = {
                items: this.cart.map(item => ({
                    productId: item.productId,
                    quantity: item.quantity,
                    unitPrice: item.price,
                    total: item.total
                })),
                subtotal: subtotal,
                discountAmount: discountAmount,
                taxAmount: taxAmount,
                total: total,
                paymentMethod: paymentMethod,
                customerId: this.currentCustomer?.id || null
            };

            console.log('💳 Processing payment:', saleData);
            
            // For now, simulate successful payment
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            this.showNotification('تم إتمام البيع بنجاح!', 'success');
            this.printReceipt(saleData);
            this.clearCart();
            
        } catch (error) {
            console.error('Error processing payment:', error);
            this.showNotification('خطأ في معالجة الدفع: ' + error.message, 'error');
        }
    }

    // Print receipt
    printReceipt(saleData) {
        // For now, just show a notification
        this.showNotification('تم طباعة الفاتورة', 'info');
        console.log('🧾 Receipt data:', saleData);
    }

    // Clear cart
    clearCart() {
        this.cart = [];
        this.discount = { type: 'none', value: 0 };
        
        // Clear discount inputs
        const discountPercent = document.getElementById('discountPercent');
        const discountAmount = document.getElementById('discountAmount');
        
        if (discountPercent) discountPercent.value = '';
        if (discountAmount) discountAmount.value = '';
        
        this.updateCartDisplay();
        this.updateTotals();
    }

    // Format currency
    formatCurrency(amount) {
        if (!amount) return '0.00 ر.س';
        return parseFloat(amount).toFixed(2) + ' ر.س';
    }

    // Debounce function
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Show notification
    showNotification(message, type = 'info') {
        if (typeof showNotification === 'function') {
            showNotification(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
}
