/**
 * Point of Sale (POS) Management System
 * Handles sales transactions, cart management, and payment processing
 */
class POSManager {
    constructor() {
        this.apiService = new ApiService();
        this.cart = [];
        this.products = [];
        this.categories = [];
        this.currentCustomer = null;
        this.discount = { type: 'none', value: 0 };
        this.tax = 0.15; // 15% VAT
        this.quantityChangeInterval = null;
        this.quantityChangeTimeout = null;

        this.init();
    }

    // Initialize POS system
    init() {
        console.log('🛒 POS Manager Initialized');
        this.setupEventListeners();
        this.loadInitialData();
        this.updateCartDisplay();
    }

    // Setup event listeners
    setupEventListeners() {
        // Product search
        const productSearch = document.getElementById('productSearch');
        if (productSearch) {
            productSearch.addEventListener('input', this.debounce((e) => {
                this.searchProducts(e.target.value);
            }, 300));
        }

        // Category filter
        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', (e) => {
                this.filterByCategory(e.target.value);
            });
        }

        // Barcode scanner
        const barcodeInput = document.getElementById('barcodeInput');
        if (barcodeInput) {
            barcodeInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.addProductByBarcode(e.target.value);
                    e.target.value = '';
                }
            });
        }

        // Discount inputs
        const discountPercent = document.getElementById('discountPercent');
        const discountAmount = document.getElementById('discountAmount');
        
        if (discountPercent) {
            discountPercent.addEventListener('input', (e) => {
                this.applyDiscount('percent', parseFloat(e.target.value) || 0);
            });
        }
        
        if (discountAmount) {
            discountAmount.addEventListener('input', (e) => {
                this.applyDiscount('amount', parseFloat(e.target.value) || 0);
            });
        }

        // Payment method buttons
        document.querySelectorAll('.payment-method').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.selectPaymentMethod(e.target.dataset.method);
            });
        });

        console.log('🎯 POS event listeners setup complete');
    }

    // Load initial data
    async loadInitialData() {
        console.log('📊 Loading POS initial data...');
        await Promise.all([
            this.loadProducts(),
            this.loadCategories()
        ]);
    }

    // Load products for POS
    async loadProducts() {
        try {
            const products = await this.apiService.get('/products');
            this.products = products.filter(p => (p.currentQuantity || 0) > 0); // Only available products
            this.renderProducts();
            console.log('📦 Loaded available products:', this.products.length);
        } catch (error) {
            console.error('Error loading products:', error);
            this.showNotification('خطأ في تحميل المنتجات', 'error');
        }
    }

    // Load categories
    async loadCategories() {
        try {
            this.categories = await this.apiService.get('/categories');
            this.renderCategoryFilter();
            console.log('📂 Loaded categories:', this.categories.length);
        } catch (error) {
            console.error('Error loading categories:', error);
        }
    }

    // Render products grid
    renderProducts(productsToRender = null) {
        const productsGrid = document.getElementById('productsGrid');
        if (!productsGrid) return;

        const products = productsToRender || this.products;

        if (products.length === 0) {
            productsGrid.innerHTML = `
                <div class="col-span-full text-center py-8">
                    <p class="text-gray-500">لا توجد منتجات متاحة</p>
                </div>
            `;
            return;
        }

        productsGrid.innerHTML = products.map(product => `
            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer ${(product.currentQuantity || 0) === 0 ? 'opacity-50' : ''}"
                 onclick="window.posManager.addToCart('${product.id}', '${product.name}', ${product.defaultSellingPrice}, ${product.currentQuantity || 0})">
                <div class="w-full h-24 bg-gray-200 rounded-lg mb-3 flex items-center justify-center overflow-hidden">
                    ${product.imageUrl ? 
                        `<img src="${product.imageUrl}" alt="${product.name}" class="w-full h-full object-cover">` :
                        `<svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>`
                    }
                </div>
                <h4 class="font-medium text-sm text-gray-900 mb-1">${product.name}</h4>
                <p class="text-xs text-gray-500 mb-2">${product.description || ''}</p>
                <p class="font-bold text-primary-600">${this.formatCurrency(product.defaultSellingPrice)}</p>
                <p class="text-xs ${(product.currentQuantity || 0) > 10 ? 'text-green-600' : (product.currentQuantity || 0) > 0 ? 'text-orange-600' : 'text-red-600'}">
                    ${(product.currentQuantity || 0) > 0 ? `متوفر: ${product.currentQuantity}` : 'نفد المخزون'}
                </p>
            </div>
        `).join('');
    }

    // Render category filter
    renderCategoryFilter() {
        const categoryFilter = document.getElementById('categoryFilter');
        if (!categoryFilter) return;

        categoryFilter.innerHTML = `
            <option value="">جميع الفئات</option>
            ${this.categories.map(category => 
                `<option value="${category.id}">${category.name}</option>`
            ).join('')}
        `;
    }

    // Search products
    searchProducts(searchTerm) {
        if (!searchTerm.trim()) {
            this.renderProducts();
            return;
        }

        const filteredProducts = this.products.filter(product => 
            product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (product.barcode && product.barcode.includes(searchTerm))
        );

        this.renderProducts(filteredProducts);
    }

    // Filter by category
    filterByCategory(categoryId) {
        if (!categoryId) {
            this.renderProducts();
            return;
        }

        const filteredProducts = this.products.filter(product => 
            product.categoryId == categoryId
        );

        this.renderProducts(filteredProducts);
    }

    // Add product by barcode
    async addProductByBarcode(barcode) {
        if (!barcode.trim()) return;

        try {
            const product = await this.apiService.get(`/products/barcode/${barcode}`);
            if (product && (product.currentQuantity || 0) > 0) {
                this.addToCart(product.id, product.name, product.defaultSellingPrice, product.currentQuantity);
                this.showNotification(`تم إضافة ${product.name} للسلة`, 'success');
            } else {
                this.showNotification('المنتج غير متوفر أو نفد المخزون', 'error');
            }
        } catch (error) {
            console.error('Error finding product by barcode:', error);
            this.showNotification('لم يتم العثور على المنتج', 'error');
        }
    }

    // Add item to cart
    addToCart(productId, name, price, availableQuantity) {
        console.log('🛒 Adding to cart:', { productId, name, price, availableQuantity });

        if (availableQuantity <= 0) {
            this.showNotification('المنتج غير متوفر', 'error');
            return;
        }

        const existingItem = this.cart.find(item => item.productId == productId);

        if (existingItem) {
            if (existingItem.quantity < availableQuantity) {
                existingItem.quantity += 1;
                existingItem.total = existingItem.quantity * existingItem.price;
                console.log('📦 Updated existing item:', existingItem);
            } else {
                this.showNotification('لا يمكن إضافة كمية أكثر من المتوفر', 'warning');
                return;
            }
        } else {
            const newItem = {
                productId: productId,
                name: name,
                price: parseFloat(price),
                quantity: 1,
                total: parseFloat(price),
                availableQuantity: availableQuantity
            };
            this.cart.push(newItem);
            console.log('✅ Added new item to cart:', newItem);
        }

        console.log('🛒 Current cart:', this.cart);
        this.updateCartDisplay();
        this.updateTotals();
        this.showNotification(`تم إضافة ${name} للسلة`, 'success');
    }

    // Remove item from cart
    removeFromCart(productId) {
        console.log('🗑️ Removing from cart:', productId);
        const itemToRemove = this.cart.find(item => item.productId == productId);
        if (itemToRemove) {
            console.log('✅ Found item to remove:', itemToRemove);
        }

        this.cart = this.cart.filter(item => item.productId != productId);
        console.log('🛒 Cart after removal:', this.cart);

        this.updateCartDisplay();
        this.updateTotals();
        this.showNotification('تم حذف المنتج من السلة', 'info');
    }

    // Update item quantity
    updateQuantity(productId, newQuantity) {
        console.log('🔢 Updating quantity for product:', productId, 'to:', newQuantity);
        const item = this.cart.find(item => item.productId == productId);
        if (!item) {
            console.log('❌ Item not found in cart');
            return;
        }

        // Ensure newQuantity is a valid number
        newQuantity = parseInt(newQuantity);
        if (isNaN(newQuantity) || newQuantity < 0) {
            newQuantity = 1;
        }

        if (newQuantity === 0) {
            console.log('🗑️ Removing item from cart');
            this.removeFromCart(productId);
            return;
        }

        if (newQuantity > item.availableQuantity) {
            this.showNotification(`الكمية المتوفرة: ${item.availableQuantity} فقط`, 'warning');
            newQuantity = item.availableQuantity;
        }

        item.quantity = newQuantity;
        item.total = item.quantity * item.price;
        console.log('✅ Updated item:', item);

        this.updateCartDisplay();
        this.updateTotals();
    }

    // Update cart display
    updateCartDisplay() {
        console.log('🔄 Updating cart display, cart length:', this.cart.length);
        const cartItems = document.getElementById('cart-items');
        if (!cartItems) {
            console.error('❌ cartItems element not found');
            return;
        }

        if (this.cart.length === 0) {
            cartItems.innerHTML = `
                <div class="text-center py-12">
                    <div class="w-16 h-16 mx-auto mb-4 bg-gray-50 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m6 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"></path>
                        </svg>
                    </div>
                    <p class="text-gray-600 font-medium">السلة فارغة</p>
                    <p class="text-sm text-gray-400 mt-1">اختر المنتجات لإضافتها</p>
                </div>
            `;
            return;
        }

        const cartHTML = this.cart.map(item => `
            <div class="group hover:bg-gray-50 rounded-lg p-3 transition-colors">
                <div class="flex items-start justify-between">
                    <div class="flex-1 min-w-0">
                        <h4 class="text-sm font-semibold text-gray-900 truncate">${item.name}</h4>
                        <p class="text-xs text-gray-500 mt-0.5">${this.formatCurrency(item.price)} للقطعة</p>
                    </div>
                    <div class="text-right ml-3">
                        <p class="text-sm font-bold text-gray-900">${this.formatCurrency(item.total)}</p>
                    </div>
                </div>

                <div class="flex items-center justify-between mt-3">
                    <div class="flex items-center bg-gray-100 rounded-lg p-1">
                        <button onclick="window.posManager.updateQuantity('${item.productId}', ${item.quantity - 1})"
                                onmousedown="window.posManager.startQuantityChange('${item.productId}', -1)"
                                onmouseup="window.posManager.stopQuantityChange()"
                                onmouseleave="window.posManager.stopQuantityChange()"
                                class="w-7 h-7 rounded-md bg-white shadow-sm flex items-center justify-center text-gray-600 hover:text-gray-800 hover:shadow transition-all">
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                            </svg>
                        </button>
                        <input type="number"
                               value="${item.quantity}"
                               min="1"
                               max="${item.availableQuantity}"
                               class="w-12 h-7 text-center text-sm font-semibold text-gray-900 bg-white border-0 rounded-md focus:ring-2 focus:ring-primary-500 focus:outline-none"
                               onchange="window.posManager.updateQuantity('${item.productId}', parseInt(this.value) || 1)"
                               onkeypress="if(event.key === 'Enter') { window.posManager.updateQuantity('${item.productId}', parseInt(this.value) || 1); this.blur(); }">
                        <button onclick="window.posManager.updateQuantity('${item.productId}', ${item.quantity + 1})"
                                onmousedown="window.posManager.startQuantityChange('${item.productId}', 1)"
                                onmouseup="window.posManager.stopQuantityChange()"
                                onmouseleave="window.posManager.stopQuantityChange()"
                                class="w-7 h-7 rounded-md bg-white shadow-sm flex items-center justify-center text-gray-600 hover:text-gray-800 hover:shadow transition-all">
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </button>
                    </div>

                    <div class="flex items-center space-x-1 space-x-reverse">
                        <!-- Quick quantity buttons -->
                        ${item.availableQuantity >= 5 ? `<button onclick="window.posManager.updateQuantity('${item.productId}', 5)" class="px-2 py-1 text-xs bg-gray-200 hover:bg-gray-300 rounded text-gray-600 transition-colors">5</button>` : ''}
                        ${item.availableQuantity >= 10 ? `<button onclick="window.posManager.updateQuantity('${item.productId}', 10)" class="px-2 py-1 text-xs bg-gray-200 hover:bg-gray-300 rounded text-gray-600 transition-colors">10</button>` : ''}

                        <button onclick="window.posManager.removeFromCart('${item.productId}')"
                                class="w-8 h-8 rounded-lg bg-gray-100 hover:bg-red-50 flex items-center justify-center text-gray-400 hover:text-red-500 transition-all group-hover:opacity-100 opacity-75 ml-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        `).join('');

        cartItems.innerHTML = cartHTML;
        console.log('✅ Cart display updated with', this.cart.length, 'items');
    }

    // Apply discount
    applyDiscount(type, value) {
        this.discount = { type, value };
        
        // Clear the other discount input
        if (type === 'percent') {
            const discountAmount = document.getElementById('discountAmount');
            if (discountAmount) discountAmount.value = '';
        } else if (type === 'amount') {
            const discountPercent = document.getElementById('discountPercent');
            if (discountPercent) discountPercent.value = '';
        }
        
        this.updateTotals();
    }

    // Update totals
    updateTotals() {
        const subtotal = this.cart.reduce((sum, item) => sum + item.total, 0);
        
        let discountAmount = 0;
        if (this.discount.type === 'percent') {
            discountAmount = subtotal * (this.discount.value / 100);
        } else if (this.discount.type === 'amount') {
            discountAmount = this.discount.value;
        }
        
        const afterDiscount = subtotal - discountAmount;
        const taxAmount = afterDiscount * this.tax;
        const total = afterDiscount + taxAmount;

        // Update display
        const subtotalElement = document.getElementById('subtotal');
        const discountElement = document.getElementById('discount');
        const taxElement = document.getElementById('tax');
        const totalElement = document.getElementById('total');

        if (subtotalElement) subtotalElement.textContent = this.formatCurrency(subtotal);
        if (discountElement) discountElement.textContent = this.formatCurrency(discountAmount);
        if (taxElement) taxElement.textContent = this.formatCurrency(taxAmount);
        if (totalElement) totalElement.textContent = this.formatCurrency(total);

        // Update cart count
        const cartCount = document.getElementById('cart-count');
        if (cartCount) {
            cartCount.textContent = this.cart.reduce((sum, item) => sum + item.quantity, 0);
        }

        console.log('💰 Totals updated - Subtotal:', subtotal, 'Total:', total);
    }

    // Process payment
    async processPayment(paymentMethod) {
        if (this.cart.length === 0) {
            this.showNotification('السلة فارغة', 'error');
            return;
        }

        try {
            // Check stock availability before processing
            for (const item of this.cart) {
                const product = this.products.find(p => p.id == item.productId);
                if (!product || (product.currentQuantity || 0) < item.quantity) {
                    this.showNotification(`المنتج ${item.name} غير متوفر بالكمية المطلوبة`, 'error');
                    return;
                }
            }

            const subtotal = this.cart.reduce((sum, item) => sum + item.total, 0);
            let discountAmount = 0;

            if (this.discount.type === 'percent') {
                discountAmount = subtotal * (this.discount.value / 100);
            } else if (this.discount.type === 'amount') {
                discountAmount = this.discount.value;
            }

            const afterDiscount = subtotal - discountAmount;
            const taxAmount = afterDiscount * this.tax;
            const total = afterDiscount + taxAmount;

            const saleData = {
                items: this.cart.map(item => ({
                    productId: item.productId,
                    quantity: item.quantity,
                    unitPrice: item.price,
                    total: item.total
                })),
                subtotal: subtotal,
                discountAmount: discountAmount,
                taxAmount: taxAmount,
                total: total,
                paymentMethod: paymentMethod,
                customerId: this.currentCustomer?.id || null,
                saleDate: new Date().toISOString()
            };

            console.log('💳 Processing payment:', saleData);

            // Send sale to backend
            const response = await this.apiService.post('/sales', saleData);

            if (response && response.id) {
                // Update local inventory
                await this.updateLocalInventory();

                this.showNotification('تم إتمام البيع بنجاح!', 'success');
                this.printReceipt({ ...saleData, id: response.id });
                this.clearCart();

                // Refresh products list to show updated quantities
                await this.loadProducts();
            } else {
                throw new Error('فشل في حفظ البيع');
            }

        } catch (error) {
            console.error('Error processing payment:', error);
            this.showNotification('خطأ في معالجة الدفع: ' + error.message, 'error');
        }
    }

    // Print receipt
    printReceipt(saleData) {
        // For now, just show a notification
        this.showNotification('تم طباعة الفاتورة', 'info');
        console.log('🧾 Receipt data:', saleData);
    }

    // Clear cart
    clearCart() {
        this.cart = [];
        this.discount = { type: 'none', value: 0 };
        
        // Clear discount inputs
        const discountPercent = document.getElementById('discountPercent');
        const discountAmount = document.getElementById('discountAmount');
        
        if (discountPercent) discountPercent.value = '';
        if (discountAmount) discountAmount.value = '';
        
        this.updateCartDisplay();
        this.updateTotals();
    }

    // Format currency
    formatCurrency(amount) {
        if (!amount) return '0.00 ر.س';
        return parseFloat(amount).toFixed(2) + ' ر.س';
    }

    // Debounce function
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Start quantity change (for long press)
    startQuantityChange(productId, direction) {
        this.quantityChangeTimeout = setTimeout(() => {
            this.quantityChangeInterval = setInterval(() => {
                const item = this.cart.find(item => item.productId == productId);
                if (item) {
                    const newQuantity = item.quantity + direction;
                    if (newQuantity > 0 && newQuantity <= item.availableQuantity) {
                        this.updateQuantity(productId, newQuantity);
                    }
                }
            }, 100); // Change every 100ms during long press
        }, 500); // Start after 500ms of holding
    }

    // Stop quantity change
    stopQuantityChange() {
        if (this.quantityChangeTimeout) {
            clearTimeout(this.quantityChangeTimeout);
            this.quantityChangeTimeout = null;
        }
        if (this.quantityChangeInterval) {
            clearInterval(this.quantityChangeInterval);
            this.quantityChangeInterval = null;
        }
    }

    // Update local inventory after sale
    async updateLocalInventory() {
        try {
            for (const item of this.cart) {
                const product = this.products.find(p => p.id == item.productId);
                if (product) {
                    // Update local quantity
                    product.currentQuantity = (product.currentQuantity || 0) - item.quantity;

                    // Send inventory update to backend
                    await this.apiService.post('/inventory/movements', {
                        productId: item.productId,
                        type: 'sale',
                        quantity: -item.quantity,
                        reason: 'بيع - نقطة البيع',
                        balanceAfter: product.currentQuantity
                    });
                }
            }

            // Update inventory manager if available
            if (window.inventoryManager) {
                window.inventoryManager.loadInventory();
            }

        } catch (error) {
            console.error('Error updating inventory:', error);
            // Don't throw error here to avoid blocking the sale completion
        }
    }

    // Print receipt
    printReceipt(saleData) {
        const receiptWindow = window.open('', '_blank', 'width=300,height=600');
        const receiptContent = `
            <!DOCTYPE html>
            <html dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>فاتورة البيع</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; font-size: 12px; }
                    .header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; margin-bottom: 15px; }
                    .item { display: flex; justify-content: space-between; margin: 5px 0; }
                    .total { border-top: 2px solid #000; padding-top: 10px; margin-top: 15px; font-weight: bold; }
                    .footer { text-align: center; margin-top: 20px; font-size: 10px; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h2>فاتورة البيع</h2>
                    <p>رقم الفاتورة: #${saleData.id || Math.floor(Math.random() * 10000)}</p>
                    <p>التاريخ: ${new Date().toLocaleDateString('ar-SA')}</p>
                    <p>الوقت: ${new Date().toLocaleTimeString('ar-SA')}</p>
                </div>

                <div class="items">
                    ${this.cart.map(item => `
                        <div class="item">
                            <span>${item.name}</span>
                            <span>${item.quantity} × ${this.formatCurrency(item.price)} = ${this.formatCurrency(item.total)}</span>
                        </div>
                    `).join('')}
                </div>

                <div class="total">
                    <div class="item">
                        <span>المجموع الفرعي:</span>
                        <span>${this.formatCurrency(saleData.subtotal)}</span>
                    </div>
                    ${saleData.discountAmount > 0 ? `
                        <div class="item">
                            <span>الخصم:</span>
                            <span>-${this.formatCurrency(saleData.discountAmount)}</span>
                        </div>
                    ` : ''}
                    <div class="item">
                        <span>الضريبة (15%):</span>
                        <span>${this.formatCurrency(saleData.taxAmount)}</span>
                    </div>
                    <div class="item" style="font-size: 14px;">
                        <span>الإجمالي:</span>
                        <span>${this.formatCurrency(saleData.total)}</span>
                    </div>
                    <div class="item">
                        <span>طريقة الدفع:</span>
                        <span>${this.getPaymentMethodName(saleData.paymentMethod)}</span>
                    </div>
                </div>

                <div class="footer">
                    <p>شكراً لتسوقكم معنا</p>
                    <p>نتمنى لكم يوماً سعيداً</p>
                </div>
            </body>
            </html>
        `;

        receiptWindow.document.write(receiptContent);
        receiptWindow.document.close();

        // Auto print after a short delay
        setTimeout(() => {
            receiptWindow.print();
        }, 500);
    }

    // Get payment method name in Arabic
    getPaymentMethodName(method) {
        const methods = {
            'cash': 'نقداً',
            'card': 'بطاقة ائتمان',
            'transfer': 'تحويل بنكي'
        };
        return methods[method] || method;
    }

    // Show notification
    showNotification(message, type = 'info') {
        if (typeof showNotification === 'function') {
            showNotification(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
}
