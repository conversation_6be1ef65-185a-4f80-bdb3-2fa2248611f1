/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["rm", [["AM", "PM"], u, u], u, [["D", "G", "M", "M", "G", "V", "S"], ["du", "gli", "ma", "me", "gie", "ve", "so"], ["dumengia", "glindesdi", "mardi", "mesemna", "gievgia", "venderdi", "sonda"], ["du", "gli", "ma", "me", "gie", "ve", "so"]], u, [["S", "F", "M", "A", "M", "Z", "F", "A", "S", "O", "N", "D"], ["schan.", "favr.", "mars", "avr.", "matg", "zercl.", "fan.", "avust", "sett.", "oct.", "nov.", "dec."], ["da schaner", "da favrer", "da mars", "d’avrigl", "da matg", "da zercladur", "da fanadur", "d’avust", "da settember", "d’october", "da november", "da december"]], [["S", "F", "M", "A", "M", "Z", "F", "A", "S", "O", "N", "D"], ["schan.", "favr.", "mars", "avr.", "matg", "zercl.", "fan.", "avust", "sett.", "oct.", "nov.", "dec."], ["schaner", "favrer", "mars", "avrigl", "matg", "zercladur", "fanadur", "avust", "settember", "october", "november", "december"]], [["av. Cr.", "s. Cr."], u, ["avant Cristus", "suenter Cristus"]], 1, [6, 0], ["dd-MM-yy", "dd-MM-y", "d MMMM y", "EEEE, 'ils' d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", "’", ";", "%", "+", "−", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0 %", "#,##0.00 ¤", "#E0"], "CHF", "CHF", "franc svizzer", { "JPY": ["JP¥", "¥"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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