import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    RouterModule
  ],
  template: `
    <div class="dashboard-container">
      <h1>لوحة التحكم</h1>
      
      <div class="stats-grid">
        <mat-card class="stat-card">
          <mat-card-header>
            <mat-icon mat-card-avatar>inventory</mat-icon>
            <mat-card-title>إجمالي المنتجات</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="stat-number">{{ totalProducts }}</div>
          </mat-card-content>
        </mat-card>

        <mat-card class="stat-card">
          <mat-card-header>
            <mat-icon mat-card-avatar>category</mat-icon>
            <mat-card-title>الأصناف</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="stat-number">{{ totalCategories }}</div>
          </mat-card-content>
        </mat-card>

        <mat-card class="stat-card">
          <mat-card-header>
            <mat-icon mat-card-avatar>business</mat-icon>
            <mat-card-title>الموردين</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="stat-number">{{ totalSuppliers }}</div>
          </mat-card-content>
        </mat-card>

        <mat-card class="stat-card">
          <mat-card-header>
            <mat-icon mat-card-avatar>storage</mat-icon>
            <mat-card-title>قيمة المخزون</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="stat-number">{{ inventoryValue | currency:'SAR':'symbol':'1.2-2' }}</div>
          </mat-card-content>
        </mat-card>
      </div>

      <div class="quick-actions">
        <h2>الإجراءات السريعة</h2>
        <div class="actions-grid">
          <button mat-raised-button color="primary" routerLink="/products">
            <mat-icon>add</mat-icon>
            إضافة منتج جديد
          </button>
          <button mat-raised-button color="accent" routerLink="/purchase-invoices">
            <mat-icon>shopping_cart</mat-icon>
            فاتورة شراء جديدة
          </button>
          <button mat-raised-button color="warn" routerLink="/sales-invoices">
            <mat-icon>receipt</mat-icon>
            فاتورة بيع جديدة
          </button>
          <button mat-raised-button routerLink="/inventory">
            <mat-icon>assessment</mat-icon>
            تقرير المخزون
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .dashboard-container {
      padding: 20px;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin: 20px 0;
    }

    .stat-card {
      text-align: center;
    }

    .stat-number {
      font-size: 2em;
      font-weight: bold;
      color: #3f51b5;
    }

    .quick-actions {
      margin-top: 40px;
    }

    .actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin-top: 20px;
    }

    .actions-grid button {
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
    }
  `]
})
export class DashboardComponent {
  totalProducts = 0;
  totalCategories = 0;
  totalSuppliers = 0;
  inventoryValue = 0;

  constructor() {
    // TODO: Load actual data from services
    this.loadDashboardData();
  }

  private loadDashboardData() {
    // Placeholder data - replace with actual service calls
    this.totalProducts = 150;
    this.totalCategories = 12;
    this.totalSuppliers = 8;
    this.inventoryValue = 125000;
  }
}
