{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["util.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;AAEH,mCAAoC;AAEpC,+BAA2B;AAKd,QAAA,wBAAwB,GAAG,+BAA+B,CAAC;AAExE,SAAgB,MAAM,CAAC,KAAc,EAAE,OAAe;IACpD,IAAI,CAAC,KAAK,EAAE;QACV,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;KAC1B;AACH,CAAC;AAJD,wBAIC;AAED,SAAgB,SAAS,CAAC,GAAkB;IAC1C,IAAI,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE;QAChE,OAAO,SAAS,CAAC;KAClB;IACD,IAAI,OAAO,GAAG,CAAC,UAAU,CAAC,IAAI,KAAK,QAAQ,EAAE;QAC3C,OAAO,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC;KAC5B;IACD,IAAI,OAAO,GAAG,CAAC,UAAU,CAAC,GAAG,KAAK,QAAQ,EAAE;QAC1C,OAAO,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC;KAC3B;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAXD,8BAWC;AAEM,MAAM,aAAa,GAAG,CAAC,CAAS,EAAU,EAAE,CACjD,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,UAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AADjC,QAAA,aAAa,iBACoB;AAEjC,QAAA,iBAAiB,GAAG,uBAAuB,CAAC;AAElD,MAAM,gBAAgB,GAAG,CAC9B,aAAuB,EACvB,MAAuB,EACf,EAAE;IACV,MAAM,MAAM,GAAG,aAAa;SACzB,GAAG,CACF,CAAC,YAAY,EAAE,EAAE,CACf,YAAY;QACZ,GAAG;QACH,mBAAU,CAAC,YAAY,CAAC;aACrB,MAAM,CACL,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CACnE;aACA,MAAM,CAAC,QAAQ,CAAC,CACtB;SACA,IAAI,CAAC,GAAG,CAAC,CAAC;IAEb,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAlBW,QAAA,gBAAgB,oBAkB3B;AAEK,MAAM,eAAe,GAAG,CAC7B,aAAuB,EACvB,EAAmB,EACX,EAAE;IACV,MAAM,WAAW,GAAG,GAAG,yBAAiB,GAAG,EAAE,EAAE,CAAC;IAChD,MAAM,MAAM,GAAG,wBAAgB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;IAC5D,OAAO,yBAAiB,GAAG,MAAM,CAAC,SAAS,CAAC,yBAAiB,CAAC,MAAM,CAAC,CAAC;AACxE,CAAC,CAAC;AAPW,QAAA,eAAe,mBAO1B;AAEF,SAAgB,UAAU,CAAC,KAAY;IACrC,MAAM,SAAS,GAAG,IAAI,GAAG,EAAS,CAAC;IACnC,MAAM,aAAa,GAAG,IAAI,GAAG,EAAU,CAAC;IAExC,SAAS,aAAa,CAAI,GAAW,EAAE,IAAO;QAC5C,IAAI,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC;YAAE,OAAO,IAAI,CAAC;QAC/B,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACd,OAAO,KAAK,CAAC;IACf,CAAC;IAED,CAAC,SAAS,YAAY,CAAC,UAAiB;QACtC,SAAS,YAAY,CAAC,KAAiB;YACrC,IAAI,aAAa,CAAC,aAAa,EAAE,KAAK,CAAC,EAAE,CAAC;gBAAE,OAAO;YACnD,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YACnC,KAAK,CAAC,gBAAgB,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,aAAa,CAAC,SAAS,EAAE,UAAU,CAAC;YAAE,OAAO;QACjD,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;IAC9D,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAEV,OAAO,SAAS,CAAC;AACnB,CAAC;AAtBD,gCAsBC;AAED,SAAgB,MAAM,CACpB,KAAgC;IAEhC,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,CAAC;AAC/C,CAAC;AAJD,wBAIC;AAED,SAAgB,2BAA2B,CACzC,MAAuB,EACvB,aAAoC;IAEpC,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,QAAe,EAAE,EAAE;QAC9D,IAAI,QAAQ,CAAC,EAAE,EAAE;YACf,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,uBAAe,CAAC,aAAa,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;SACtE;QACD,OAAO,SAAS,CAAC;IACnB,CAAC,EAAE,EAA+B,CAAC,CAAC;AACtC,CAAC;AAVD,kEAUC;AAED,QAAQ,CAAC,CAAC,SAAS,CAAI,IAAsB;IAC3C,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;IAC7D,IAAI,CAAC,UAAU,EAAE;QACf,OAAO;KACR;IAED,cAAc,EAAE,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE;QAC7C,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;YACtB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBAClB,SAAS,cAAc,CAAC;aACzB;SACF;QACD,MAAM,IAAI,CAAC;KACZ;AACH,CAAC;AAED,QAAQ,CAAC,CAAC,GAAG,CACX,KAAkB,EAClB,EAAwB;IAExB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;QACxB,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC;KAChB;AACH,CAAC;AAED,QAAQ,CAAC,CAAC,OAAO,CACf,WAAwB,EACxB,EAAkC;IAElC,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE;QAC9B,KAAK,MAAM,MAAM,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE;YAC7B,MAAM,MAAM,CAAC;SACd;KACF;AACH,CAAC;AAQD;;;GAGG;AACH,SAAS,kBAAkB,CAAI,EAC7B,QAAQ,EACR,KAAK,GACI;;IACT,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,MAAM,KAAK,GAAQ,EAAE,CAAC;IACtB,MAAM,cAAc,GAAG,IAAI,GAAG,CAC5B,GAAG,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CACxC,CAAC;IAEF,MAAM,2BAA2B,GAAG,IAAI,GAAG,EAAiC,CAAC;IAE7E,SAAS,aAAa,CAAC,MAAS;;QAC9B,yDAAyD;QACzD,MAAM,UAAU,GAAG,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC9C,MAAM,CAAC,UAAU,EAAE,yBAAyB,CAAC,CAAC;QAC9C,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC;QACzB,UAAU,CAAC,OAAO,GAAG,KAAK,CAAC;QAC3B,KAAK,EAAE,CAAC;QACR,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACnB,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC;QAE1B,KAAK,MAAM,KAAK,IAAI,MAAA,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,mCAAI,EAAE,EAAE;YAC3C,MAAM,SAAS,GAAG,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,CAAC,SAAS,EAAE,+BAA+B,CAAC,CAAC;YACnD,IAAI,SAAS,CAAC,KAAK,KAAK,SAAS,EAAE;gBACjC,gDAAgD;gBAChD,aAAa,CAAC,KAAK,CAAC,CAAC;gBACrB,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAC3B,UAAU,CAAC,OAAO,EAClB,MAAA,SAAS,CAAC,OAAO,mCAAI,QAAQ,CAC9B,CAAC;aACH;iBAAM,IAAI,SAAS,CAAC,OAAO,EAAE;gBAC5B,iDAAiD;gBACjD,iHAAiH;gBACjH,qDAAqD;gBACrD,gGAAgG;gBAChG,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;aACpE;SACF;QAED,8DAA8D;QAC9D,IAAI,UAAU,CAAC,KAAK,KAAK,UAAU,CAAC,OAAO,EAAE;YAC3C,MAAM,6BAA6B,GAAG,EAAE,KAAK,EAAE,IAAI,GAAG,EAAK,EAAE,CAAC;YAC9D,IAAI,WAA0B,CAAC;YAC/B,GAAG;gBACD,WAAW,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;gBAC1B,MAAM,CAAC,WAAW,EAAE,yBAAyB,CAAC,CAAC;gBAC/C,MAAM,QAAQ,GAAG,cAAc,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBACjD,MAAM,CAAC,QAAQ,EAAE,yCAAyC,CAAC,CAAC;gBAC5D,QAAQ,CAAC,OAAO,GAAG,KAAK,CAAC;gBACzB,6BAA6B,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;aACtD,QAAQ,WAAW,KAAK,MAAM,EAAE;YAEjC,2BAA2B,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;SAChE;IACH,CAAC;IAED,KAAK,MAAM,MAAM,IAAI,QAAQ,EAAE;QAC7B,MAAM,IAAI,GAAG,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACxC,MAAM,CAAC,IAAI,EAAE,2BAA2B,CAAC,CAAC;QAC1C,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;YAC5B,aAAa,CAAC,MAAM,CAAC,CAAC;SACvB;KACF;IAED,4DAA4D;IAC5D,MAAM,cAAc,GAAG,IAAI,GAAG,EAAoC,CAAC;IACnE,MAAM,QAAQ,GAAG,IAAI,GAAG,EAGrB,CAAC;IAEJ,KAAK,MAAM,GAAG,IAAI,2BAA2B,EAAE;QAC7C,KAAK,MAAM,MAAM,IAAI,GAAG,CAAC,KAAK,EAAE;YAC9B,cAAc,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;SACjC;KACF;IAED,KAAK,MAAM,GAAG,IAAI,2BAA2B,EAAE;QAC7C,MAAM,aAAa,GAAG,IAAI,GAAG,EAAiC,CAAC;QAC/D,KAAK,MAAM,MAAM,IAAI,GAAG,CAAC,KAAK,EAAE;YAC9B,KAAK,MAAM,WAAW,IAAI,MAAA,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,mCAAI,EAAE,EAAE;gBACjD,MAAM,QAAQ,GAAG,cAAc,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBACjD,IAAI,QAAQ,IAAI,QAAQ,KAAK,GAAG,EAAE;oBAChC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;iBAC7B;aACF;SACF;QACD,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;KAClC;IAED,OAAO,EAAE,QAAQ,EAAE,2BAA2B,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;AACpE,CAAC;AAED,qGAAqG;AACrG,wCAAwC;AACxC,SAAS,eAAe,CAAI,EAAE,QAAQ,EAAE,KAAK,EAAY;IACvD,MAAM,WAAW,GAAQ,EAAE,CAAC;IAE5B,MAAM,SAAS,GAAG,IAAI,GAAG,EAAK,CAAC;IAE/B,SAAS,KAAK,CAAC,IAAO;;QACpB,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACvB,OAAO;SACR;QAED,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAEpB,KAAK,MAAM,KAAK,IAAI,MAAA,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,mCAAI,EAAE,EAAE;YACzC,KAAK,CAAC,KAAK,CAAC,CAAC;SACd;QAED,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAED,KAAK,MAAM,MAAM,IAAI,QAAQ,EAAE;QAC7B,KAAK,CAAC,MAAM,CAAC,CAAC;KACf;IAED,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,SAAgB,kCAAkC,CAChD,MAAuB;;IAMvB,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAS,CAAC;IAClC,MAAM,KAAK,GAAG,IAAI,GAAG,EAAqB,CAAC;IAE3C,2DAA2D;IAC3D,KAAK,MAAM,MAAM,IAAI,MAAM,EAAE;QAC3B,IAAI,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YACxB,SAAS;SACV;QACD,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACrB,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,GAAG,EAAS,CAAC,CAAC;QACpC,KAAK,MAAM,WAAW,IAAI,MAAM,CAAC,cAAc,EAAE;YAC/C,KAAK,MAAM,UAAU,IAAI,WAAW,CAAC,gBAAgB,EAAE;gBACrD,KAAK,MAAM,UAAU,IAAI,UAAU,CAAC,MAAM,EAAE;oBAC1C,MAAA,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,0CAAE,GAAG,CAAC,UAAU,CAAC,CAAC;iBACpC;aACF;SACF;KACF;IAED,MAAM,GAAG,GAAG,kBAAkB,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC;IACpD,MAAM,cAAc,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC;IAC5C,MAAM,aAAa,GAAG,IAAI,GAAG,CAC3B,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,CACxE,CAAC;IAEF,OAAO,CAAC,cAAc,EAAE,GAAG,EAAE,aAAa,CAAC,CAAC;AAC9C,CAAC;AAjCD,gFAiCC;AAED,SAAgB,qBAAqB,CACnC,MAAuB;;IAKvB,MAAM,CAAC,cAAc,EAAE,AAAD,EAAG,aAAa,CAAC,GACrC,kCAAkC,CAAC,MAAM,CAAC,CAAC;IAE7C,sFAAsF;IACtF,mEAAmE;IACnE,2FAA2F;IAC3F,sEAAsE;IACtE,MAAM,4BAA4B,GAAG,IAAI,GAAG,EAA0B,CAAC;IAEvE,qEAAqE;IACrE,MAAM,aAAa,GAAG,IAAI,GAAG,EAAqB,CAAC;IAEnD,SAAS,aAAa,CAAI,eAAiC;QACzD,OAAO,IAAI,GAAG,CAAI,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC;IAChD,CAAC;IAED,SAAS,4BAA4B,CAAC,KAAY;;QAChD,MAAM,eAAe,GAAiB,EAAE,CAAC;QACzC,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,cAAc,EAAE;YACxC,KAAK,MAAM,MAAM,IAAI,KAAK,CAAC,eAAe,EAAE;gBAC1C,eAAe,CAAC,IAAI,CAClB,MAAA,4BAA4B,CAAC,GAAG,CAAC,MAAM,CAAC,mCAAI,IAAI,GAAG,EAAS,CAC7D,CAAC;aACH;SACF;QAED,OAAO,aAAa,CAAC,eAAe,CAAC,CAAC;IACxC,CAAC;IAED,SAAS,kCAAkC,CAAC,KAAY;;QACtD,MAAM,WAAW,GAAG,IAAI,GAAG,EAAS,CAAC;QACrC,MAAM,QAAQ,GAAG,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAE1C,KAAK,MAAM,UAAU,IAAI,KAAK,CAAC,cAAc,EAAE;YAC7C,IAAI,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,KAAK,EAAE;gBAC7D,qDAAqD;gBACrD,yDAAyD;gBACzD,SAAS;aACV;YACD,KAAK,MAAM,UAAU,IAAI,UAAU,CAAC,gBAAgB,EAAE;gBACpD,KAAK,MAAM,UAAU,IAAI,UAAU,CAAC,MAAM,EAAE;oBAC1C,MAAM,aAAa,GAAG,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;oBACpD,IAAI,aAAa,KAAK,QAAQ,EAAE;wBAC9B,8BAA8B;wBAC9B,yDAAyD;wBACzD,SAAS;qBACV;oBACD,KAAK,MAAM,iBAAiB,IAAI,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,KAAK,mCAAI,EAAE,EAAE;wBAC1D,WAAW,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;qBACpC;iBACF;aACF;SACF;QAED,MAAM,cAAc,GAAG,4BAA4B,CAAC,KAAK,CAAC,CAAC;QAC3D,KAAK,MAAM,aAAa,IAAI,cAAc,EAAE;YAC1C,WAAW,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;SACnC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,yDAAyD;IACzD,KAAK,IAAI,CAAC,GAAG,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;QACnD,MAAM,GAAG,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;QAC9B,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,KAAK,EAAE;YAC7B,MAAM,QAAQ,GAAG,kCAAkC,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,sBAAsB,GAAG,4BAA4B,CAAC,KAAK,CAAC,CAAC;YAEnE,KAAK,MAAM,KAAK,IAAI,QAAQ,EAAE;gBAC5B,IAAI,sBAAsB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;oBACrC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;iBACxB;qBAAM;oBACL,sBAAsB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;iBACnC;aACF;YAED,aAAa,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;YACnC,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,cAAc,EAAE;gBACxC,2CAA2C;gBAC3C,MAAM,qBAAqB,GAAG,aAAa,CACzC,GAAG,CACD,KAAK,CAAC,eAAe,EACrB,CAAC,MAAM,EAAE,EAAE,WACT,OAAA,MAAA,4BAA4B,CAAC,GAAG,CAAC,MAAM,CAAC,mCAAI,IAAI,GAAG,EAAS,CAAA,EAAA,CAC/D,CACF,CAAC;gBACF,4BAA4B;gBAC5B,KAAK,MAAM,KAAK,IAAI,QAAQ,EAAE;oBAC5B,qBAAqB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;iBAClC;gBACD,+CAA+C;gBAC/C,KAAK,MAAM,KAAK,IAAI,MAAA,4BAA4B,CAAC,GAAG,CAAC,KAAK,CAAC,mCACzD,IAAI,GAAG,EAAS,EAAE;oBAClB,qBAAqB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;iBAClC;gBACD,4BAA4B,CAAC,GAAG,CAAC,KAAK,EAAE,qBAAqB,CAAC,CAAC;aAChE;SACF;KACF;IAED,OAAO,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;AACzC,CAAC;AA5GD,sDA4GC"}