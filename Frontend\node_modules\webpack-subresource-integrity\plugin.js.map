{"version": 3, "file": "plugin.js", "sourceRoot": "", "sources": ["plugin.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;;;;;;;;;;;;;;;;;;;AAGH,+BAAsC;AACtC,2BAAkC;AAClC,qDAAuC;AAOvC,iCASgB;AAyBhB,MAAM,sBAAsB,GAA4C;IACtE,CAAC,IAAI,EAAE,aAAa,CAAC;IACrB,CAAC,KAAK,EAAE,cAAc,CAAC;CACxB,CAAC;AAEF,MAAa,MAAM;IA8CjB,YACE,WAAwB,EACxB,OAAkD,EAClD,QAAkB;QAjCpB;;WAEG;QACK,mBAAc,GAAwB,IAAI,GAAG,EAAE,CAAC;QAExD;;WAEG;QACK,0BAAqB,GAAwB,IAAI,GAAG,EAAE,CAAC;QAE/D;;WAEG;QACK,kBAAa,GAAkB,IAAI,CAAC;QAE5C;;WAEG;QACK,oBAAe,GAAwC,EAAE,CAAC;QAElE;;WAEG;QACK,kBAAa,GAA2B,IAAI,GAAG,EAAqB,CAAC;QAE7E;;WAEG;QACK,kBAAa,GAAG,IAAI,GAAG,EAA2B,CAAC;QAkC3D;;WAEG;QACH,8BAAyB,GAAG,CAC1B,MAAsC,EAChC,EAAE;YACR,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACvC,MAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAC/B,IAAI,MAAM,CAAC;gBACX,IAAI;oBACF,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;iBACzB;gBAAC,OAAO,CAAC,EAAE;oBACV,OAAO;iBACR;gBACD,IAAI,CAAC,oBAAoB,CACvB,QAAQ,EACR,uBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,MAAM,CAAC,CACrD,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;QAEF;;WAEG;QACK,iBAAY,GAAG,CACrB,QAAkB,EAClB,MAAsC,EACtC,aAA2C,EAC3C,SAAiB,EACD,EAAE;YAClB,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,CAAC;YAC7C,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;YACjD,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,CACzD,MAAM,CAAC,SAAS,CAAC,EACjB,SAAS,CACV,CAAC;YAEF,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;gBACxD,MAAM,WAAW,GAAG,sBAAe,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjE,MAAM,cAAc,GAAG,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;gBACtD,IAAI,cAAc,IAAI,CAAC,EAAE;oBACvB,QAAQ,CAAC,OAAO,CACd,cAAc,EACd,cAAc,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,EACvC,SAAS,CAAC,CAAC,CAAC,EACZ,SAAS,CACV,CAAC;iBACH;YACH,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,SAAS,CAAC,GAAG,QAAQ,CAAC;YAE7B,OAAO,QAAQ,CAAC;QAClB,CAAC,CAAC;QAEM,6BAAwB,GAAG,CAAC,SAAoB,EAAE,EAAE;YAC1D,IACE,CAAC,SAAS,CAAC,QAAQ;gBACjB,SAAS,CAAC,SAAS;gBACnB,SAAS,CAAC,UAAU;gBACpB,SAAS,CAAC,WAAW,CAAC;gBACxB,CAAC,CACC,SAAS,CAAC,WAAW;oBACrB,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,eAAe,CAC/D,EACD;gBACA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CACpB;;;kBAGU,CACX,CAAC;aACH;QACH,CAAC,CAAC;QAEF;;WAEG;QACK,iBAAY,GAAG,CACrB,KAAY,EACZ,MAAsC,EAChC,EAAE;YACR,KAAK,CAAC,IAAI,CAAC,iBAAU,CAAC,KAAK,CAAC,CAAC;iBAC1B,OAAO,EAAE;iBACT,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;QAChE,CAAC,CAAC;QAEM,uBAAkB,GAAG,CAC3B,UAAiB,EACjB,MAAsC,EACtC,EAAE;YACF,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAE3C,KAAK,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBAC3B,IAAI,MAAM,CAAC,UAAU,CAAC,EAAE;oBACtB,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;oBAClD,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAChC,IAAI,CAAC,WAAW,CAAC,QAAQ,EACzB,MAAM,EACN,IAAI,CAAC,aAAa,EAClB,UAAU,CACX,CAAC;oBACF,MAAM,SAAS,GAAG,uBAAgB,CAChC,IAAI,CAAC,OAAO,CAAC,aAAa,EAC1B,QAAQ,CAAC,MAAM,EAAE,CAClB,CAAC;oBAEF,IAAI,UAAU,CAAC,EAAE,KAAK,IAAI,EAAE;wBAC1B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;qBAClD;oBACD,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;oBACjD,IAAI,CAAC,WAAW,CAAC,WAAW,CAC1B,UAAU,EACV,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EACR,CAAC,SAAS,EAAE,EAAE;wBACZ,IAAI,CAAC,SAAS,EAAE;4BACd,OAAO,SAAS,CAAC;yBAClB;wBAED,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC;wBAEzC,OAAO;4BACL,GAAG,SAAS;4BACZ,WAAW,EAAE,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC;gCAC/C,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,CAAC;gCACrD,CAAC,CAAC,SAAS,CAAC,WAAW;oCACvB,CAAC,CAAC,CAAC,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC;oCACpC,CAAC,CAAC,SAAS;yBACd,CAAC;oBACJ,CAAC,CACF,CAAC;iBACH;qBAAM;oBACL,IAAI,CAAC,QAAQ,CAAC,QAAQ,CACpB,mCAAmC,UAAU,kBAAkB,MAAM,CAAC,IAAI,CACxE,MAAM,CACP,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CACf,CAAC;iBACH;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;QAEF;;WAEG;QACH,iBAAY,GAAG,CAAC,MAAc,EAAE,MAAc,EAAU,EAAE;YACxD,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,kBAAkB,EAAE;gBACtD,IAAI,CAAC,QAAQ,CAAC,SAAS,CACrB,iFAAiF,CAClF,CAAC;aACH;YAED,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBACzD,MAAM;gBACN,MAAM,GAAG,gBAAgB,+BAAwB,YAAY;gBAC7D,MAAM;oBACJ,iBAAiB;oBACjB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,kBAAkB,CAAC;oBACjE,GAAG;aACN,CAAC,CAAC;QACL,CAAC,CAAC;QAEF;;WAEG;QACH,kBAAa,GAAG,CAAC,MAAsC,EAAQ,EAAE;YAC/D,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,MAAM,EAAE;gBACvC,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,eAAe,EAAE;oBACtC,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,KAAK,EAAE;wBAC7B,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;qBACxC;iBACF;aACF;iBAAM;gBACL,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;qBAChC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;qBACrC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;oBACjB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;gBACnC,CAAC,CAAC,CAAC;aACN;YAED,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;QACzC,CAAC,CAAC;QAEF;;WAEG;QACK,iBAAY,GAAG,CAAC,GAAW,EAAU,EAAE;YAC7C,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACrC,OAAO,eAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;QAC3C,CAAC,CAAC;QAEF;;WAEG;QACK,iCAA4B,GAAG,CACrC,MAAsC,EACtC,GAAW,EACS,EAAE;YACtB,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBAChC,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;aACrC;YAED,MAAM,aAAa,GAAG,oBAAa,CAAC,GAAG,CAAC,CAAC;YACzC,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAC5C,CAAC,QAAQ,EAAE,EAAE,CAAC,oBAAa,CAAC,QAAQ,CAAC,KAAK,aAAa,CACxD,CAAC;YACF,IAAI,aAAa,EAAE;gBACjB,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;aAC/C;YACD,OAAO,SAAS,CAAC;QACnB,CAAC,CAAC;QAEF;;WAEG;QACH,eAAU,GAAG,CAAC,GAAkB,EAAQ,EAAE;YACxC,IACE,GAAG,CAAC,UAAU;gBACd,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,WAAW,CAAC,EACjE;gBACA,OAAO;aACR;YAED,MAAM,MAAM,GAAG,gBAAS,CAAC,GAAG,CAAC,CAAC;YAE9B,IAAI,CAAC,MAAM,EAAE;gBACX,OAAO;aACR;YAED,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YAEtC,GAAG,CAAC,UAAU,CAAC,SAAS;gBACtB,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,CAAC;oBAC/D,uBAAgB,CACd,IAAI,CAAC,OAAO,CAAC,aAAa,EAC1B,iBAAY,CAAC,WAAI,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,CAC9D,CAAC;YACJ,GAAG,CAAC,UAAU,CAAC,WAAW;gBACxB,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,kBAAkB;oBAC3D,WAAW,CAAC;QAChB,CAAC,CAAC;QAEF;;WAEG;QACH,8BAAyB,GAAG,GAAS,EAAE;YACrC,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,MAAM,EAAE;gBACvC,MAAM,CAAC,eAAe,EAAE,aAAa,CAAC,GAAG,4BAAqB,CAC5D,IAAI,CAAC,WAAW,CAAC,MAAM,CACxB,CAAC;gBACF,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;gBACvC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;aACpC;YACD,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC7B,CAAC,CAAC;QAMF,wBAAmB,GAAG,CAAC,EAAE,MAAM,EAAyB,EAAQ,EAAE;YAChE,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,UAAU,CAAC;YAEvC,sBAAsB,CAAC,OAAO,CAC5B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAwC,EAAE,EAAE;gBAChD,IAAI,CAAC,EAAE;oBACJ,MAAuB,CAAC,CAAC,CAAC,GAAI,MAAwB,CAAC,CAAC,CAAC;yBACvD,GAAG,CAAC,CAAC,QAAgB,EAAE,EAAE,CACxB,IAAI,CAAC,4BAA4B,CAC/B,IAAI,CAAC,WAAW,CAAC,MAAM,EACvB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAC5B,CACF;yBACA,MAAM,CAAC,aAAM,CAAC,CAAC;iBACnB;YACH,CAAC,CACF,CAAC;QACJ,CAAC,CAAC;QAEF,eAAU,GAAG,CAAC,KAAe,EAAE,OAAe,EAAsB,EAAE;YACpE,MAAM,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACzD,IAAI,QAAQ,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;gBAClC,MAAM,YAAY,GAAG,uBAAgB,CACnC,IAAI,CAAC,OAAO,CAAC,aAAa,EAC1B,KAAK,CAAC,CAAC,CAAC,CACT,CAAC;gBACF,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBAC3C,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBACrC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;gBAElD,OAAO,YAAY,CAAC;aACrB;YACD,OAAO,SAAS,CAAC;QACnB,CAAC,CAAC;QAEF,sBAAiB,GAAG,CAAC,EACnB,QAAQ,EACR,QAAQ,GAIT,EAAQ,EAAE;YACT,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAExD,QAAQ;iBACL,MAAM,CAAC,QAAQ,CAAC;iBAChB,OAAO,CAAC,CAAC,GAAkB,EAAE,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;QAC3D,CAAC,CAAC;QA7UA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,MAAuB;QAC7C,IAAI,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE;YAC3C,IAAI,CAAC,QAAQ,CAAC,QAAQ,CACpB,kEAAkE;gBAChE,qDAAqD,CACxD,CAAC;SACH;IACH,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,QAAgB,EAAE,SAAiB;QAC9D,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;YACtC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;YAC7C,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;SACrD;IACH,CAAC;IAiQD,kCAAkC,CAAC,KAAY;;QAC7C,OAAO,MAAA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,mCAAI,IAAI,GAAG,EAAS,CAAC;IAC3D,CAAC;CAkDF;AAjYD,wBAiYC"}