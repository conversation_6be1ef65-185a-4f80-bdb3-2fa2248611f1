// ===== Dashboard Management =====

let dashboardData = {
    stats: {},
    activities: [],
    alerts: []
};

// Initialize Dashboard
async function initializeDashboard() {
    console.log('🚀 Initializing Dashboard...');
    
    // Check authentication
    if (!requireAuth()) {
        return;
    }
    
    // Load user info
    loadUserInfo();
    
    // Show loading
    showLoading(true);
    
    try {
        // Load all dashboard data
        await Promise.all([
            loadDashboardStats(),
            loadRecentActivities(),
            loadSystemAlerts()
        ]);
        
        console.log('✅ Dashboard initialized successfully');
        
    } catch (error) {
        console.error('❌ Dashboard initialization failed:', error);
        showMessage('فشل في تحميل لوحة التحكم: ' + formatApiError(error), 'error');
        
    } finally {
        showLoading(false);
    }
}

// Load User Info
function loadUserInfo() {
    const userData = getStoredUserData();
    
    if (userData) {
        const userNameElement = document.getElementById('userName');
        const userRoleElement = document.getElementById('userRole');
        
        if (userNameElement) {
            userNameElement.textContent = `مرحباً ${userData.fullName || userData.username}`;
        }
        
        if (userRoleElement) {
            userRoleElement.textContent = userData.roleName || 'مستخدم';
        }
        
        console.log('👤 User info loaded:', userData);
    }
}

// Load Dashboard Stats
async function loadDashboardStats() {
    console.log('📊 Loading dashboard stats...');
    
    const api = new ApiService();
    
    try {
        // Try to get stats from dashboard endpoint first
        try {
            const stats = await api.get(API_ENDPOINTS.DASHBOARD_STATS);
            dashboardData.stats = stats;
            updateStatsDisplay(stats);
            console.log('✅ Dashboard stats loaded from API:', stats);
            return;
        } catch (error) {
            console.warn('⚠️ Dashboard stats API failed, loading individual endpoints:', error);
        }
        
        // Fallback: Load individual endpoints
        const [categories, products, suppliers] = await Promise.all([
            api.get(API_ENDPOINTS.CATEGORIES),
            api.get(API_ENDPOINTS.PRODUCTS),
            api.get(API_ENDPOINTS.SUPPLIERS)
        ]);
        
        const fallbackStats = {
            totalProducts: products.length,
            totalCategories: categories.length,
            totalSuppliers: suppliers.length,
            inventoryValue: 0, // Will be calculated later
            lowStockItems: 0,
            outOfStockItems: 0
        };
        
        dashboardData.stats = fallbackStats;
        updateStatsDisplay(fallbackStats);
        console.log('✅ Dashboard stats loaded from fallback:', fallbackStats);
        
    } catch (error) {
        console.error('❌ Failed to load dashboard stats:', error);
        throw error;
    }
}

// Update Stats Display
function updateStatsDisplay(stats) {
    const elements = {
        totalProducts: document.getElementById('totalProducts'),
        totalCategories: document.getElementById('totalCategories'),
        totalSuppliers: document.getElementById('totalSuppliers'),
        inventoryValue: document.getElementById('inventoryValue')
    };
    
    if (elements.totalProducts) {
        elements.totalProducts.textContent = formatNumber(stats.totalProducts || 0);
    }
    
    if (elements.totalCategories) {
        elements.totalCategories.textContent = formatNumber(stats.totalCategories || 0);
    }
    
    if (elements.totalSuppliers) {
        elements.totalSuppliers.textContent = formatNumber(stats.totalSuppliers || 0);
    }
    
    if (elements.inventoryValue) {
        elements.inventoryValue.textContent = formatCurrency(stats.inventoryValue || 0);
    }
    
    // Animate numbers
    animateNumbers();
}

// Load Recent Activities
async function loadRecentActivities() {
    console.log('🕒 Loading recent activities...');
    
    const api = new ApiService();
    
    try {
        const activities = await api.get(API_ENDPOINTS.DASHBOARD_ACTIVITIES);
        dashboardData.activities = activities;
        updateActivitiesDisplay(activities);
        console.log('✅ Recent activities loaded:', activities);
        
    } catch (error) {
        console.warn('⚠️ Failed to load recent activities:', error);
        
        // Show fallback message
        const activitiesContainer = document.getElementById('recentActivities');
        if (activitiesContainer) {
            activitiesContainer.innerHTML = `
                <div class="no-data">
                    <i class="fas fa-info-circle"></i>
                    <p>لا توجد أنشطة حديثة متاحة</p>
                </div>
            `;
        }
    }
}

// Update Activities Display
function updateActivitiesDisplay(activities) {
    const container = document.getElementById('recentActivities');
    if (!container) return;
    
    if (!activities || activities.length === 0) {
        container.innerHTML = `
            <div class="no-data">
                <i class="fas fa-info-circle"></i>
                <p>لا توجد أنشطة حديثة</p>
            </div>
        `;
        return;
    }
    
    const activitiesHtml = activities.slice(0, 10).map(activity => `
        <div class="activity-item">
            <div class="activity-icon">
                <i class="fas ${getActivityIcon(activity.movementType)}"></i>
            </div>
            <div class="activity-content">
                <div class="activity-title">${activity.productName}</div>
                <div class="activity-time">
                    ${activity.movementType} - ${formatQuantity(activity.quantity)} - ${formatDateTime(activity.createdAt)}
                </div>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = activitiesHtml;
}

// Load System Alerts
async function loadSystemAlerts() {
    console.log('🚨 Loading system alerts...');
    
    const api = new ApiService();
    
    try {
        const alerts = await api.get(API_ENDPOINTS.DASHBOARD_ALERTS);
        dashboardData.alerts = alerts;
        updateAlertsDisplay(alerts);
        console.log('✅ System alerts loaded:', alerts);
        
    } catch (error) {
        console.warn('⚠️ Failed to load system alerts:', error);
        
        // Show fallback message
        const alertsContainer = document.getElementById('systemAlerts');
        if (alertsContainer) {
            alertsContainer.innerHTML = `
                <div class="no-data">
                    <i class="fas fa-info-circle"></i>
                    <p>لا توجد تنبيهات متاحة</p>
                </div>
            `;
        }
    }
}

// Update Alerts Display
function updateAlertsDisplay(alerts) {
    const container = document.getElementById('systemAlerts');
    if (!container) return;
    
    const allAlerts = [];
    
    // Add low stock alerts
    if (alerts.lowStock && alerts.lowStock.length > 0) {
        allAlerts.push(...alerts.lowStock.map(item => ({
            type: 'warning',
            title: 'مخزون قليل',
            description: item.message,
            icon: 'fa-exclamation-triangle'
        })));
    }
    
    // Add out of stock alerts
    if (alerts.outOfStock && alerts.outOfStock.length > 0) {
        allAlerts.push(...alerts.outOfStock.map(item => ({
            type: 'error',
            title: 'نفد المخزون',
            description: item.message,
            icon: 'fa-times-circle'
        })));
    }
    
    if (allAlerts.length === 0) {
        container.innerHTML = `
            <div class="no-data">
                <i class="fas fa-check-circle" style="color: #48bb78;"></i>
                <p>لا توجد تنبيهات - النظام يعمل بشكل طبيعي</p>
            </div>
        `;
        return;
    }
    
    const alertsHtml = allAlerts.slice(0, 10).map(alert => `
        <div class="alert-item">
            <div class="alert-icon ${alert.type}">
                <i class="fas ${alert.icon}"></i>
            </div>
            <div class="alert-content">
                <div class="alert-title">${alert.title}</div>
                <div class="alert-description">${alert.description}</div>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = alertsHtml;
}

// Show/Hide Loading
function showLoading(show) {
    const loadingElement = document.getElementById('dashboardLoading');
    if (loadingElement) {
        loadingElement.style.display = show ? 'flex' : 'none';
    }
}

// Refresh Dashboard
async function refreshDashboard() {
    console.log('🔄 Refreshing dashboard...');
    
    // Add rotation animation to refresh button
    const refreshBtn = event.target.closest('.btn-icon');
    if (refreshBtn) {
        refreshBtn.style.transform = 'rotate(360deg)';
        setTimeout(() => {
            refreshBtn.style.transform = '';
        }, 500);
    }
    
    await initializeDashboard();
    showMessage('تم تحديث لوحة التحكم بنجاح', 'success');
}

// ===== Expense Modal Functions =====

function openExpenseModal() {
    const modal = document.getElementById('expenseModal');
    if (modal) {
        modal.classList.add('show');
        
        // Focus on first input
        const firstInput = modal.querySelector('input');
        if (firstInput) {
            setTimeout(() => firstInput.focus(), 100);
        }
    }
}

function closeExpenseModal() {
    const modal = document.getElementById('expenseModal');
    if (modal) {
        modal.classList.remove('show');
        
        // Reset form
        const form = document.getElementById('expenseForm');
        if (form) {
            form.reset();
        }
    }
}

// Setup Expense Form
document.addEventListener('DOMContentLoaded', function() {
    const expenseForm = document.getElementById('expenseForm');
    if (expenseForm) {
        expenseForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(expenseForm);
            const expenseData = {
                expenseType: formData.get('expenseType').trim(),
                amount: parseFloat(formData.get('amount')),
                note: formData.get('note').trim()
            };
            
            // Validate
            if (!expenseData.expenseType || expenseData.amount <= 0) {
                showMessage('يرجى إدخال نوع المصروف والمبلغ بشكل صحيح', 'error');
                return;
            }
            
            try {
                console.log('💰 Creating expense:', expenseData);
                
                const api = new ApiService();
                const result = await api.post(API_ENDPOINTS.EXPENSES, expenseData);
                
                console.log('✅ Expense created:', result);
                showMessage('تم إضافة المصروف بنجاح', 'success');
                
                closeExpenseModal();
                
                // Refresh activities
                await loadRecentActivities();
                
            } catch (error) {
                console.error('❌ Failed to create expense:', error);
                showMessage('فشل في إضافة المصروف: ' + formatApiError(error), 'error');
            }
        });
    }
    
    // Close modal when clicking outside
    const modal = document.getElementById('expenseModal');
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeExpenseModal();
            }
        });
    }
});

// ===== Navigation Functions =====

function viewProducts() {
    // For now, just show a message
    showMessage('صفحة المنتجات قيد التطوير', 'info');
}

function viewCategories() {
    // For now, just show a message
    showMessage('صفحة الأصناف قيد التطوير', 'info');
}

function viewSuppliers() {
    // For now, just show a message
    showMessage('صفحة الموردين قيد التطوير', 'info');
}

// ===== Utility Functions =====

function formatNumber(num) {
    return new Intl.NumberFormat('ar-SA').format(num);
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 0
    }).format(amount);
}

function formatQuantity(quantity) {
    const absQuantity = Math.abs(quantity);
    const sign = quantity >= 0 ? '+' : '-';
    return `${sign}${absQuantity}`;
}

function formatDateTime(dateString) {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('ar-SA', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    }).format(date);
}

function getActivityIcon(movementType) {
    switch (movementType?.toLowerCase()) {
        case 'sale':
        case 'بيع':
            return 'fa-shopping-cart';
        case 'purchase':
        case 'شراء':
            return 'fa-truck';
        case 'return':
        case 'إرجاع':
            return 'fa-undo';
        default:
            return 'fa-exchange-alt';
    }
}

function animateNumbers() {
    const numberElements = document.querySelectorAll('.stat-number');
    
    numberElements.forEach(element => {
        const finalValue = element.textContent;
        element.textContent = '0';
        
        // Simple animation
        let current = 0;
        const increment = Math.ceil(parseInt(finalValue.replace(/[^\d]/g, '')) / 20);
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= parseInt(finalValue.replace(/[^\d]/g, ''))) {
                element.textContent = finalValue;
                clearInterval(timer);
            } else {
                element.textContent = formatNumber(current);
            }
        }, 50);
    });
}

function showMessage(message, type = 'info') {
    const container = document.getElementById('messageContainer');
    if (!container) return;
    
    const messageElement = document.createElement('div');
    messageElement.className = `message ${type}`;
    
    const icon = type === 'success' ? 'fa-check-circle' : 
                 type === 'error' ? 'fa-exclamation-triangle' : 
                 'fa-info-circle';
    
    messageElement.innerHTML = `
        <i class="fas ${icon}"></i>
        <span>${message}</span>
    `;
    
    container.appendChild(messageElement);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (messageElement.parentNode) {
            messageElement.parentNode.removeChild(messageElement);
        }
    }, 5000);
}
