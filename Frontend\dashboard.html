<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة المتجر الإلكتروني</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="dashboard-page">
    <!-- Header -->
    <header class="dashboard-header">
        <div class="header-content">
            <div class="logo-section">
                <i class="fas fa-store"></i>
                <h1>متجر الإلكترونيات</h1>
            </div>
            
            <div class="user-section">
                <div class="user-info">
                    <span class="user-name" id="userName">مرحباً</span>
                    <span class="user-role" id="userRole">Admin</span>
                </div>
                <div class="user-actions">
                    <button class="btn-icon" onclick="refreshDashboard()" title="تحديث">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button class="btn-icon" onclick="logout()" title="تسجيل الخروج">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="dashboard-main">
        <!-- Loading Spinner -->
        <div id="dashboardLoading" class="loading-overlay">
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <p>جاري تحميل لوحة التحكم...</p>
            </div>
        </div>

        <!-- Welcome Section -->
        <section class="welcome-section">
            <div class="welcome-content">
                <h1 class="welcome-title">مرحباً بك في ElectroHub Pro</h1>
                <p class="welcome-subtitle">نظام شامل لإدارة متجرك الإلكتروني</p>
            </div>
        </section>

        <!-- Stats Cards -->
        <section class="stats-section">
            <div class="stats-grid">
                <!-- المنتجات المتاحة -->
                <div class="stat-card available-products">
                    <div class="stat-icon">
                        <i class="fas fa-box"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="totalProducts">2,847</div>
                        <div class="stat-label">المنتجات المتاحة</div>
                        <div class="stat-change positive">+15 منذ أمس</div>
                    </div>
                </div>

                <!-- إجمالي المبيعات اليوم -->
                <div class="stat-card daily-sales">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="dailySales">15,750 <span class="currency">ريال</span></div>
                        <div class="stat-label">إجمالي المبيعات اليوم</div>
                        <div class="stat-change positive">+12.5% من أمس</div>
                    </div>
                </div>

                <!-- المنتجات المنخفضة -->
                <div class="stat-card low-stock">
                    <div class="stat-icon warning">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="lowStockCount">8</div>
                        <div class="stat-label">منتجات منخفضة</div>
                        <div class="stat-note">تحتاج مراجعة</div>
                    </div>
                </div>

                <!-- الطلبات المعلقة -->
                <div class="stat-card pending-orders">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="pendingOrders">23</div>
                        <div class="stat-label">طلبات معلقة</div>
                        <div class="stat-note">منذ قليل من الوقت</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Main Dashboard Content -->
        <div class="dashboard-content">
            <!-- Left Column -->
            <div class="left-column">
                <!-- أحدث المعاملات -->
                <section class="transactions-section">
                    <div class="section-header">
                        <h3>أحدث المعاملات</h3>
                    </div>
                    <div class="transactions-list" id="recentTransactions">
                        <!-- Transactions will be loaded here -->
                    </div>
                </section>
            </div>

            <!-- Right Column -->
            <div class="right-column">
                <!-- إجراءات سريعة -->
                <section class="quick-actions-section">
                    <div class="section-header">
                        <h3>إجراءات سريعة</h3>
                    </div>
                    <div class="quick-actions">
                        <button class="quick-action-btn add-product" onclick="openExpenseModal()">
                            <i class="fas fa-plus"></i>
                            إضافة منتج
                        </button>
                    </div>
                </section>

                <!-- منتج نفدت الكمية -->
                <section class="out-of-stock-section">
                    <div class="section-header">
                        <h3>منتج نفدت الكمية</h3>
                        <button class="view-all-btn" onclick="viewProducts()">
                            <i class="fas fa-external-link-alt"></i>
                            عرض الجميع
                        </button>
                    </div>
                    <div class="out-of-stock-list" id="outOfStockProducts">
                        <!-- Out of stock products will be loaded here -->
                    </div>
                </section>

                <!-- تقرير المبيعات -->
                <section class="sales-report-section">
                    <div class="section-header">
                        <h3>تقرير المبيعات</h3>
                        <button class="view-report-btn" onclick="viewReports()">
                            <i class="fas fa-chart-bar"></i>
                            عرض التقرير
                        </button>
                    </div>
                    <div class="sales-summary" id="salesSummary">
                        <!-- Sales summary will be loaded here -->
                    </div>
                </section>
            </div>
        </div>
    </main>

    <!-- Add Expense Modal -->
    <div id="expenseModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-plus-circle"></i> إضافة مصروف جديد</h3>
                <button class="close-btn" onclick="closeExpenseModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="expenseForm" class="modal-form">
                <div class="form-group">
                    <label for="expenseType">نوع المصروف</label>
                    <input type="text" id="expenseType" name="expenseType" required placeholder="مثال: كهرباء، إيجار، مواد تنظيف">
                </div>
                
                <div class="form-group">
                    <label for="expenseAmount">المبلغ</label>
                    <input type="number" id="expenseAmount" name="amount" step="0.01" min="0" required placeholder="0.00">
                </div>
                
                <div class="form-group">
                    <label for="expenseNote">ملاحظات</label>
                    <textarea id="expenseNote" name="note" rows="3" placeholder="تفاصيل إضافية عن المصروف (اختياري)"></textarea>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save"></i>
                        حفظ المصروف
                    </button>
                    <button type="button" class="btn-secondary" onclick="closeExpenseModal()">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Error/Success Messages -->
    <div id="messageContainer" class="message-container"></div>

    <!-- Scripts -->
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/dashboard.js"></script>
    
    <script>
        // Initialize dashboard when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeDashboard();
        });
    </script>
</body>
</html>
