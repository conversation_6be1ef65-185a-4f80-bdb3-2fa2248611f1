<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة المتجر الإلكتروني</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="dashboard-page">
    <!-- Header -->
    <header class="dashboard-header">
        <div class="header-content">
            <div class="logo-section">
                <i class="fas fa-store"></i>
                <h1>متجر الإلكترونيات</h1>
            </div>
            
            <div class="user-section">
                <div class="user-info">
                    <span class="user-name" id="userName">مرحباً</span>
                    <span class="user-role" id="userRole">Admin</span>
                </div>
                <div class="user-actions">
                    <button class="btn-icon" onclick="refreshDashboard()" title="تحديث">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button class="btn-icon" onclick="logout()" title="تسجيل الخروج">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="dashboard-main">
        <!-- Loading Spinner -->
        <div id="dashboardLoading" class="loading-overlay">
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <p>جاري تحميل لوحة التحكم...</p>
            </div>
        </div>

        <!-- Stats Cards -->
        <section class="stats-section">
            <h2 class="section-title">
                <i class="fas fa-chart-bar"></i>
                الإحصائيات العامة
            </h2>
            
            <div class="stats-grid">
                <div class="stat-card products">
                    <div class="stat-icon">
                        <i class="fas fa-box"></i>
                    </div>
                    <div class="stat-content">
                        <h3>المنتجات</h3>
                        <p class="stat-number" id="totalProducts">0</p>
                        <span class="stat-label">إجمالي المنتجات</span>
                    </div>
                </div>

                <div class="stat-card categories">
                    <div class="stat-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="stat-content">
                        <h3>الأصناف</h3>
                        <p class="stat-number" id="totalCategories">0</p>
                        <span class="stat-label">إجمالي الأصناف</span>
                    </div>
                </div>

                <div class="stat-card suppliers">
                    <div class="stat-icon">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div class="stat-content">
                        <h3>الموردين</h3>
                        <p class="stat-number" id="totalSuppliers">0</p>
                        <span class="stat-label">إجمالي الموردين</span>
                    </div>
                </div>

                <div class="stat-card inventory">
                    <div class="stat-icon">
                        <i class="fas fa-warehouse"></i>
                    </div>
                    <div class="stat-content">
                        <h3>قيمة المخزون</h3>
                        <p class="stat-number" id="inventoryValue">0</p>
                        <span class="stat-label">ريال سعودي</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Quick Actions -->
        <section class="actions-section">
            <h2 class="section-title">
                <i class="fas fa-bolt"></i>
                العمليات السريعة
            </h2>
            
            <div class="actions-grid">
                <button class="action-btn" onclick="openExpenseModal()">
                    <i class="fas fa-plus-circle"></i>
                    <span>إضافة مصروف</span>
                </button>
                
                <button class="action-btn" onclick="viewProducts()">
                    <i class="fas fa-eye"></i>
                    <span>عرض المنتجات</span>
                </button>
                
                <button class="action-btn" onclick="viewCategories()">
                    <i class="fas fa-list"></i>
                    <span>عرض الأصناف</span>
                </button>
                
                <button class="action-btn" onclick="viewSuppliers()">
                    <i class="fas fa-building"></i>
                    <span>عرض الموردين</span>
                </button>
            </div>
        </section>

        <!-- Recent Activities -->
        <section class="activities-section">
            <h2 class="section-title">
                <i class="fas fa-history"></i>
                الأنشطة الحديثة
            </h2>
            
            <div class="activities-container">
                <div id="recentActivities" class="activities-list">
                    <!-- Activities will be loaded here -->
                </div>
            </div>
        </section>

        <!-- Alerts -->
        <section class="alerts-section">
            <h2 class="section-title">
                <i class="fas fa-exclamation-triangle"></i>
                التنبيهات
            </h2>
            
            <div class="alerts-container">
                <div id="systemAlerts" class="alerts-list">
                    <!-- Alerts will be loaded here -->
                </div>
            </div>
        </section>
    </main>

    <!-- Add Expense Modal -->
    <div id="expenseModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-plus-circle"></i> إضافة مصروف جديد</h3>
                <button class="close-btn" onclick="closeExpenseModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="expenseForm" class="modal-form">
                <div class="form-group">
                    <label for="expenseType">نوع المصروف</label>
                    <input type="text" id="expenseType" name="expenseType" required placeholder="مثال: كهرباء، إيجار، مواد تنظيف">
                </div>
                
                <div class="form-group">
                    <label for="expenseAmount">المبلغ</label>
                    <input type="number" id="expenseAmount" name="amount" step="0.01" min="0" required placeholder="0.00">
                </div>
                
                <div class="form-group">
                    <label for="expenseNote">ملاحظات</label>
                    <textarea id="expenseNote" name="note" rows="3" placeholder="تفاصيل إضافية عن المصروف (اختياري)"></textarea>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save"></i>
                        حفظ المصروف
                    </button>
                    <button type="button" class="btn-secondary" onclick="closeExpenseModal()">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Error/Success Messages -->
    <div id="messageContainer" class="message-container"></div>

    <!-- Scripts -->
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/dashboard.js"></script>
    
    <script>
        // Initialize dashboard when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeDashboard();
        });
    </script>
</body>
</html>
