-- ============================================================================
-- فحص جدول المستخدمين قبل التحديث
-- Check Users Table Before Update
-- ============================================================================
-- 
-- قم بتشغيل هذا السكريبت أولاً للتحقق من حالة البيانات الحالية
-- Run this script first to check the current state of your data
--
-- ============================================================================

PRINT '🔍 فحص جدول المستخدمين الحالي...';
PRINT 'Checking current users table...';
PRINT '';

-- 1. التحقق من وجود الجدول
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'users')
BEGIN
    PRINT '❌ خطأ: جدول users غير موجود!';
    PRINT '❌ ERROR: users table does not exist!';
    RETURN;
END

PRINT '✅ جدول users موجود';
PRINT '✅ users table exists';
PRINT '';

-- 2. عرض هيكل الجدول الحالي
PRINT '📋 هيكل الجدول الحالي:';
PRINT '📋 Current table structure:';
PRINT '';

SELECT 
    COLUMN_NAME as 'Column Name',
    DATA_TYPE as 'Data Type',
    CHARACTER_MAXIMUM_LENGTH as 'Max Length',
    IS_NULLABLE as 'Nullable',
    COLUMN_DEFAULT as 'Default Value'
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'users'
ORDER BY ORDINAL_POSITION;

PRINT '';

-- 3. عرض إحصائيات البيانات الحالية
PRINT '📊 إحصائيات البيانات الحالية:';
PRINT '📊 Current data statistics:';
PRINT '';

SELECT 
    'إجمالي المستخدمين / Total Users' as Statistic,
    COUNT(*) as Count
FROM users

UNION ALL

SELECT 
    'المستخدمون بكلمات مرور طويلة / Users with long passwords',
    COUNT(*)
FROM users 
WHERE LEN(password) > 100

UNION ALL

SELECT 
    'المستخدمون بأسماء مستخدم فريدة / Users with unique usernames',
    COUNT(DISTINCT username)
FROM users;

PRINT '';

-- 4. التحقق من الحقول التي سيتم إضافتها
PRINT '🔍 فحص الحقول المطلوب إضافتها:';
PRINT '🔍 Checking fields to be added:';
PRINT '';

-- فحص حقل email
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('users') AND name = 'email')
    PRINT '⚠️ حقل email موجود بالفعل'
ELSE
    PRINT '✅ حقل email سيتم إضافته';

-- فحص حقل full_name
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('users') AND name = 'full_name')
    PRINT '⚠️ حقل full_name موجود بالفعل'
ELSE
    PRINT '✅ حقل full_name سيتم إضافته';

-- فحص حقل phone_number
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('users') AND name = 'phone_number')
    PRINT '⚠️ حقل phone_number موجود بالفعل'
ELSE
    PRINT '✅ حقل phone_number سيتم إضافته';

-- فحص حقل is_active
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('users') AND name = 'is_active')
    PRINT '⚠️ حقل is_active موجود بالفعل'
ELSE
    PRINT '✅ حقل is_active سيتم إضافته';

-- فحص حقل last_login_at
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('users') AND name = 'last_login_at')
    PRINT '⚠️ حقل last_login_at موجود بالفعل'
ELSE
    PRINT '✅ حقل last_login_at سيتم إضافته';

PRINT '';

-- 5. فحص حجم حقل password
PRINT '🔐 فحص حقل كلمة المرور:';
PRINT '🔐 Checking password field:';

SELECT 
    'حجم حقل password الحالي / Current password field size' as Info,
    CHARACTER_MAXIMUM_LENGTH as 'Max Length'
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'password';

IF (SELECT CHARACTER_MAXIMUM_LENGTH FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'password') < 255
BEGIN
    PRINT '⚠️ حقل password صغير (أقل من 255) - سيتم توسيعه';
    PRINT '⚠️ password field is small (less than 255) - will be expanded';
END
ELSE
BEGIN
    PRINT '✅ حقل password بالحجم المناسب';
    PRINT '✅ password field has appropriate size';
END

PRINT '';

-- 6. عرض عينة من البيانات الحالية
PRINT '📄 عينة من البيانات الحالية:';
PRINT '📄 Sample of current data:';
PRINT '';

SELECT TOP 5
    id,
    username,
    LEN(password) as password_length,
    role_id,
    created_at
FROM users
ORDER BY id;

PRINT '';

-- 7. التوصيات
PRINT '💡 التوصيات:';
PRINT '💡 Recommendations:';
PRINT '';
PRINT '1. ✅ قم بعمل نسخة احتياطية من قاعدة البيانات قبل التحديث';
PRINT '1. ✅ Create a database backup before updating';
PRINT '';
PRINT '2. ✅ تأكد من عدم وجود تطبيقات تستخدم الجدول أثناء التحديث';
PRINT '2. ✅ Ensure no applications are using the table during update';
PRINT '';
PRINT '3. ✅ قم بتشغيل سكريبت UpdateUserTable.sql بعد هذا الفحص';
PRINT '3. ✅ Run UpdateUserTable.sql script after this check';
PRINT '';

PRINT '============================================================================';
PRINT '🎯 جاهز للتحديث! قم بتشغيل UpdateUserTable.sql';
PRINT '🎯 Ready for update! Run UpdateUserTable.sql';
PRINT '============================================================================';
