/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// Converts a string that represents a URL into a URL class instance.
export function getUrl(src, win) {
    // Don't use a base URL is the URL is absolute.
    return isAbsoluteUrl(src) ? new URL(src) : new URL(src, win.location.href);
}
// Checks whether a URL is absolute (i.e. starts with `http://` or `https://`).
export function isAbsoluteUrl(src) {
    return /^https?:\/\//.test(src);
}
// Given a URL, extract the hostname part.
// If a URL is a relative one - the URL is returned as is.
export function extractHostname(url) {
    return isAbsoluteUrl(url) ? new URL(url).hostname : url;
}
export function isValidPath(path) {
    const isString = typeof path === 'string';
    if (!isString || path.trim() === '') {
        return false;
    }
    // Calling new URL() will throw if the path string is malformed
    try {
        const url = new URL(path);
        return true;
    }
    catch {
        return false;
    }
}
export function normalizePath(path) {
    return path.endsWith('/') ? path.slice(0, -1) : path;
}
export function normalizeSrc(src) {
    return src.startsWith('/') ? src.slice(1) : src;
}
//# sourceMappingURL=data:application/json;base64,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