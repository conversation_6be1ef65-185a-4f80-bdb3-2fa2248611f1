using Microsoft.EntityFrameworkCore;
using ElectronicsStore.Domain.Entities;
using ElectronicsStore.Domain.Enums;

namespace ElectronicsStore.Infrastructure.Data;

public class ElectronicsStoreDbContext : DbContext
{
    public ElectronicsStoreDbContext(DbContextOptions<ElectronicsStoreDbContext> options) : base(options)
    {
    }

    // DbSets
    public DbSet<Category> Categories { get; set; }
    public DbSet<Supplier> Suppliers { get; set; }
    public DbSet<Product> Products { get; set; }
    public DbSet<Role> Roles { get; set; }
    public DbSet<Permission> Permissions { get; set; }
    public DbSet<RolePermission> RolePermissions { get; set; }
    public DbSet<User> Users { get; set; }
    public DbSet<PurchaseInvoice> PurchaseInvoices { get; set; }
    public DbSet<PurchaseInvoiceDetail> PurchaseInvoiceDetails { get; set; }
    public DbSet<SalesInvoice> SalesInvoices { get; set; }
    public DbSet<SalesInvoiceDetail> SalesInvoiceDetails { get; set; }
    public DbSet<InventoryLog> InventoryLogs { get; set; }
    public DbSet<SalesReturn> SalesReturns { get; set; }
    public DbSet<PurchaseReturn> PurchaseReturns { get; set; }
    public DbSet<Expense> Expenses { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure table names to match database schema
        modelBuilder.Entity<Category>().ToTable("categories");
        modelBuilder.Entity<Supplier>().ToTable("suppliers");
        modelBuilder.Entity<Product>().ToTable("products");
        modelBuilder.Entity<Role>().ToTable("roles");
        modelBuilder.Entity<Permission>().ToTable("permissions");
        modelBuilder.Entity<RolePermission>().ToTable("role_permissions");
        modelBuilder.Entity<User>().ToTable("users");
        modelBuilder.Entity<PurchaseInvoice>().ToTable("purchase_invoices");
        modelBuilder.Entity<PurchaseInvoiceDetail>().ToTable("purchase_invoice_details");
        modelBuilder.Entity<SalesInvoice>().ToTable("sales_invoices");
        modelBuilder.Entity<SalesInvoiceDetail>().ToTable("sales_invoice_details");
        modelBuilder.Entity<InventoryLog>().ToTable("inventory_logs");
        modelBuilder.Entity<SalesReturn>().ToTable("sales_returns");
        modelBuilder.Entity<PurchaseReturn>().ToTable("purchase_returns");
        modelBuilder.Entity<Expense>().ToTable("expenses");

        // Configure primary keys and column names
        ConfigureEntities(modelBuilder);
        
        // Configure relationships
        ConfigureRelationships(modelBuilder);
        
        // Configure enums
        ConfigureEnums(modelBuilder);
    }

    private void ConfigureEntities(ModelBuilder modelBuilder)
    {
        // Category
        modelBuilder.Entity<Category>(entity =>
        {
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Name).HasColumnName("name").HasMaxLength(100).IsRequired();
            entity.Property(e => e.CreatedAt).HasColumnName("created_at").HasDefaultValueSql("GETDATE()");
        });

        // Supplier
        modelBuilder.Entity<Supplier>(entity =>
        {
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Name).HasColumnName("name").HasMaxLength(100).IsRequired();
            entity.Property(e => e.Phone).HasColumnName("phone").HasMaxLength(20);
            entity.Property(e => e.Email).HasColumnName("email").HasMaxLength(100);
            entity.Property(e => e.Address).HasColumnName("address").HasMaxLength(200);
            entity.Property(e => e.CreatedAt).HasColumnName("created_at").HasDefaultValueSql("GETDATE()");
        });

        // Product
        modelBuilder.Entity<Product>(entity =>
        {
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Name).HasColumnName("name").HasMaxLength(150).IsRequired();
            entity.Property(e => e.Barcode).HasColumnName("barcode").HasMaxLength(50);
            entity.Property(e => e.CategoryId).HasColumnName("category_id");
            entity.Property(e => e.SupplierId).HasColumnName("supplier_id");
            entity.Property(e => e.DefaultCostPrice).HasColumnName("default_cost_price").HasColumnType("decimal(10,2)");
            entity.Property(e => e.DefaultSellingPrice).HasColumnName("default_selling_price").HasColumnType("decimal(10,2)");
            entity.Property(e => e.MinSellingPrice).HasColumnName("min_selling_price").HasColumnType("decimal(10,2)");
            entity.Property(e => e.Description).HasColumnName("description").HasMaxLength(500);
            entity.Property(e => e.CreatedAt).HasColumnName("created_at").HasDefaultValueSql("GETDATE()");
        });
    }

    private void ConfigureRelationships(ModelBuilder modelBuilder)
    {
        // RolePermission - Many to Many
        modelBuilder.Entity<RolePermission>()
            .HasKey(rp => new { rp.RoleId, rp.PermissionId });

        modelBuilder.Entity<RolePermission>()
            .Property(rp => rp.RoleId).HasColumnName("role_id");

        modelBuilder.Entity<RolePermission>()
            .Property(rp => rp.PermissionId).HasColumnName("permission_id");

        // Product relationships
        modelBuilder.Entity<Product>()
            .HasOne(p => p.Category)
            .WithMany(c => c.Products)
            .HasForeignKey(p => p.CategoryId);

        modelBuilder.Entity<Product>()
            .HasOne(p => p.Supplier)
            .WithMany(s => s.Products)
            .HasForeignKey(p => p.SupplierId);

        // User relationships
        modelBuilder.Entity<User>()
            .HasOne(u => u.Role)
            .WithMany(r => r.Users)
            .HasForeignKey(u => u.RoleId);
    }

    private void ConfigureEnums(ModelBuilder modelBuilder)
    {
        // PaymentMethod enum
        modelBuilder.Entity<SalesInvoice>()
            .Property(e => e.PaymentMethod)
            .HasColumnName("payment_method")
            .HasConversion(
                v => v.ToString().ToLower(),
                v => (PaymentMethod)Enum.Parse(typeof(PaymentMethod), v, true));

        // MovementType enum
        modelBuilder.Entity<InventoryLog>()
            .Property(e => e.MovementType)
            .HasColumnName("movement_type")
            .HasConversion(
                v => v.ToString().ToLower(),
                v => (MovementType)Enum.Parse(typeof(MovementType), v, true));
    }
}
