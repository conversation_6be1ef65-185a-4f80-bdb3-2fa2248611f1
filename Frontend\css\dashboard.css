/* ===== Dashboard Specific Styles ===== */

.dashboard-page {
    background: #f5f7fa;
    min-height: 100vh;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* ===== Header Styles ===== */
.dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-section i {
    font-size: 2rem;
    color: rgba(255, 255, 255, 0.9);
}

.logo-section h1 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.user-section {
    display: flex;
    align-items: center;
    gap: 20px;
}

.user-info {
    text-align: left;
}

.user-name {
    display: block;
    font-weight: 600;
    font-size: 1rem;
}

.user-role {
    display: block;
    font-size: 0.85rem;
    opacity: 0.8;
    margin-top: 2px;
}

.user-actions {
    display: flex;
    gap: 10px;
}

.btn-icon {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-icon:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* ===== Main Content ===== */
.dashboard-main {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    position: relative;
}

/* Welcome Section */
.welcome-section {
    text-align: center;
    margin-bottom: 2rem;
}

.welcome-title {
    color: #1e293b;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.welcome-subtitle {
    color: #64748b;
    font-size: 1.1rem;
    margin: 0;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.section-title i {
    color: #667eea;
}

/* ===== Loading Overlay ===== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-overlay .loading-spinner {
    text-align: center;
    color: #667eea;
}

.loading-overlay .loading-spinner i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.loading-overlay .loading-spinner p {
    font-size: 1.1rem;
    color: #666;
}

/* ===== Stats Section ===== */
.stats-section {
    margin-bottom: 3rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    color: white;
    flex-shrink: 0;
}

.stat-card.available-products .stat-icon {
    background: #3b82f6;
}

.stat-card.daily-sales .stat-icon {
    background: #10b981;
}

.stat-card.low-stock .stat-icon {
    background: #f59e0b;
}

.stat-card.low-stock .stat-icon.warning {
    background: #f59e0b;
}

.stat-card.pending-orders .stat-icon {
    background: #8b5cf6;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 0.25rem 0;
    line-height: 1;
}

.stat-number .currency {
    font-size: 1rem;
    font-weight: 500;
    color: #64748b;
}

.stat-label {
    font-size: 0.9rem;
    color: #64748b;
    margin-bottom: 0.25rem;
    display: block;
}

.stat-change {
    font-size: 0.8rem;
    font-weight: 500;
}

.stat-change.positive {
    color: #10b981;
}

.stat-note {
    font-size: 0.8rem;
    color: #64748b;
}

/* ===== Dashboard Content Layout ===== */
.dashboard-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-top: 2rem;
}

.left-column,
.right-column {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* ===== Section Headers ===== */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.section-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
}

/* ===== Transactions Section ===== */
.transactions-section {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.transactions-list {
    max-height: 400px;
    overflow-y: auto;
}

/* ===== Quick Actions Section ===== */
.quick-actions-section {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.quick-action-btn {
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.quick-action-btn:hover {
    background: #2563eb;
    transform: translateY(-1px);
}

.quick-action-btn.add-product {
    background: #3b82f6;
}

/* ===== Out of Stock Section ===== */
.out-of-stock-section {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.view-all-btn {
    background: none;
    border: none;
    color: #3b82f6;
    cursor: pointer;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    transition: color 0.3s ease;
}

.view-all-btn:hover {
    color: #2563eb;
}

.out-of-stock-list {
    max-height: 200px;
    overflow-y: auto;
}

/* ===== Sales Report Section ===== */
.sales-report-section {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.view-report-btn {
    background: none;
    border: none;
    color: #3b82f6;
    cursor: pointer;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    transition: color 0.3s ease;
}

.view-report-btn:hover {
    color: #2563eb;
}

.sales-summary {
    max-height: 200px;
    overflow-y: auto;
}

/* ===== Activities Section ===== */
.activities-section,
.alerts-section {
    margin-bottom: 3rem;
}

.activities-container,
.alerts-container {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.activities-list,
.alerts-list {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item,
.alert-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid #f0f0f0;
    transition: background 0.3s ease;
}

.activity-item:hover,
.alert-item:hover {
    background: #f8f9fa;
}

.activity-item:last-child,
.alert-item:last-child {
    border-bottom: none;
}

.activity-icon,
.alert-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
}

.activity-icon {
    background: #667eea;
}

.alert-icon.warning {
    background: #f6ad55;
}

.alert-icon.error {
    background: #fc8181;
}

.alert-icon.info {
    background: #63b3ed;
}

.activity-content,
.alert-content {
    flex: 1;
}

.activity-title,
.alert-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.25rem;
}

.activity-time,
.alert-description {
    font-size: 0.85rem;
    color: #666;
}

/* ===== Modal Styles ===== */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    animation: slideUp 0.3s ease;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e1e5e9;
}

.modal-header h3 {
    margin: 0;
    color: #333;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #666;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: #f0f0f0;
    color: #333;
}

.modal-form {
    padding: 1.5rem;
}

.modal-form .form-group {
    margin-bottom: 1.5rem;
}

.modal-form label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 600;
}

.modal-form input,
.modal-form textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.modal-form input:focus,
.modal-form textarea:focus {
    outline: none;
    border-color: #667eea;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

.btn-primary,
.btn-secondary {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a67d8;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-secondary:hover {
    background: #cbd5e0;
}

/* ===== Message Container ===== */
.message-container {
    position: fixed;
    top: 100px;
    right: 20px;
    z-index: 1100;
    max-width: 400px;
}

.message {
    background: white;
    border-radius: 8px;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-left: 4px solid;
    animation: slideInRight 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.message.success {
    border-left-color: #48bb78;
    color: #2f855a;
}

.message.error {
    border-left-color: #f56565;
    color: #c53030;
}

.message.info {
    border-left-color: #4299e1;
    color: #2b6cb0;
}

/* ===== Animations ===== */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ===== No Data Styles ===== */
.no-data {
    text-align: center;
    padding: 2rem;
    color: #666;
}

.no-data i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.no-data p {
    font-size: 1.1rem;
    margin: 0;
}

/* ===== Transaction Items ===== */
.transaction-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid #f1f5f9;
    transition: background-color 0.3s ease;
}

.transaction-item:hover {
    background-color: #f8fafc;
}

.transaction-item:last-child {
    border-bottom: none;
}

.transaction-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.transaction-icon.sale {
    background-color: #10b981;
}

.transaction-icon.purchase {
    background-color: #f59e0b;
}

.transaction-details {
    flex: 1;
}

.transaction-description {
    font-weight: 500;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.transaction-time {
    font-size: 0.8rem;
    color: #64748b;
}

.transaction-amount {
    font-weight: 600;
    font-size: 0.9rem;
}

.transaction-amount.sale {
    color: #10b981;
}

.transaction-amount.purchase {
    color: #f59e0b;
}

/* ===== Out of Stock Items ===== */
.out-of-stock-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #f1f5f9;
    transition: background-color 0.3s ease;
}

.out-of-stock-item:hover {
    background-color: #f8fafc;
}

.out-of-stock-item:last-child {
    border-bottom: none;
}

.product-info {
    flex: 1;
}

.product-name {
    font-weight: 500;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.product-category {
    font-size: 0.8rem;
    color: #64748b;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-badge.out-of-stock {
    background-color: #fef2f2;
    color: #dc2626;
}

/* ===== Sales Summary ===== */
.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f5f9;
}

.summary-item:last-child {
    border-bottom: none;
}

.summary-label {
    font-size: 0.9rem;
    color: #64748b;
}

.summary-value {
    font-weight: 600;
    color: #1e293b;
}

.summary-value.positive {
    color: #10b981;
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    .dashboard-header {
        padding: 1rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .dashboard-main {
        padding: 1rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .dashboard-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .form-actions {
        flex-direction: column;
    }

    .message-container {
        right: 10px;
        left: 10px;
        max-width: none;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .welcome-title {
        font-size: 1.5rem;
    }

    .welcome-subtitle {
        font-size: 1rem;
    }
}
